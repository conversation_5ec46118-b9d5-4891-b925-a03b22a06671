import React from 'react';
import { <PERSON>, <PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>er, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { AlertTriangle, RefreshCw, Home } from 'lucide-react';
import { Link } from 'react-router-dom';
import { createPageUrl } from '@/utils';

class ErrorBoundary extends React.Component {
  constructor(props) {
    super(props);
    this.state = { hasError: false, error: null, errorInfo: null };
  }

  static getDerivedStateFromError(error) {
    return { hasError: true };
  }

  componentDidCatch(error, errorInfo) {
    this.setState({
      error: error,
      errorInfo: errorInfo
    });
  }

  render() {
    if (this.state.hasError) {
      return (
        <div className="min-h-screen bg-white p-6 font-mono flex items-center justify-center">
          <Card className="border-4 border-black shadow-[12px_12px_0px_0px_#FF0066] bg-white transform -rotate-2 max-w-2xl w-full">
            <CardHeader className="border-b-4 border-black bg-[#FF0066] text-white">
              <CardTitle className="text-3xl font-black flex items-center gap-4">
                <AlertTriangle className="w-8 h-8" />
                BRUTAL ERROR DETECTED
              </CardTitle>
            </CardHeader>
            <CardContent className="p-8 text-center">
              <div className="mb-8">
                <h2 className="text-2xl font-black mb-4">SOMETHING WENT WRONG!</h2>
                <p className="font-bold text-gray-600 mb-4">
                  Our brutal system encountered an unexpected error. Don't worry, we're on it.
                </p>
                
                {/* Remove process.env check and show error info only in development */}
                {this.state.error && (
                  <div className="p-4 border-4 border-black bg-black text-white text-left font-mono text-sm mt-6">
                    <p className="text-[#FF0066] font-bold mb-2">ERROR INFO:</p>
                    <p className="text-[#00FF66]">{this.state.error.toString()}</p>
                    {this.state.errorInfo?.componentStack && (
                      <pre className="text-[#FFFF00] mt-2 overflow-auto text-xs">
                        {this.state.errorInfo.componentStack.slice(0, 500)}...
                      </pre>
                    )}
                  </div>
                )}
              </div>
              
              <div className="flex flex-col sm:flex-row gap-4 justify-center">
                <Button
                  onClick={() => window.location.reload()}
                  className="font-black border-4 border-black shadow-[4px_4px_0px_0px_#000000] bg-[#00FF66] text-black hover:bg-[#00FF66]/90 text-lg px-6 py-3"
                >
                  <RefreshCw className="w-5 h-5 mr-2" />
                  RELOAD PAGE
                </Button>
                
                <Link to={createPageUrl('Home')}>
                  <Button
                    className="font-black border-4 border-black shadow-[4px_4px_0px_0px_#000000] bg-[#0066FF] text-white hover:bg-[#0066FF]/90 text-lg px-6 py-3"
                  >
                    <Home className="w-5 h-5 mr-2" />
                    GO HOME
                  </Button>
                </Link>
              </div>
            </CardContent>
          </Card>
        </div>
      );
    }

    return this.props.children;
  }
}

export default ErrorBoundary;