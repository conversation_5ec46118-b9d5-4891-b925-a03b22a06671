const express = require('express');
const bcrypt = require('bcryptjs');
const { body, validationResult } = require('express-validator');
const { User } = require('../models');
const { generateToken, generateRefreshToken, refreshToken } = require('../middleware/auth');
const { endpointLimiters } = require('../middleware/rateLimiter');
const logger = require('../utils/logger');

const router = express.Router();

// Validation middleware
const validateRegistration = [
  body('email')
    .isEmail()
    .normalizeEmail()
    .withMessage('Please provide a valid email address'),
  body('password')
    .isLength({ min: 8 })
    .withMessage('Password must be at least 8 characters long')
    .matches(/^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[@$!%*?&])[A-Za-z\d@$!%*?&]/)
    .withMessage('Password must contain at least one uppercase letter, one lowercase letter, one number, and one special character'),
  body('firstName')
    .trim()
    .isLength({ min: 1, max: 50 })
    .withMessage('First name must be between 1 and 50 characters'),
  body('lastName')
    .trim()
    .isLength({ min: 1, max: 50 })
    .withMessage('Last name must be between 1 and 50 characters')
];

const validateLogin = [
  body('email')
    .isEmail()
    .normalizeEmail()
    .withMessage('Please provide a valid email address'),
  body('password')
    .notEmpty()
    .withMessage('Password is required')
];

const validatePasswordReset = [
  body('email')
    .isEmail()
    .normalizeEmail()
    .withMessage('Please provide a valid email address')
];

// Helper function to handle validation errors
const handleValidationErrors = (req, res, next) => {
  const errors = validationResult(req);
  if (!errors.isEmpty()) {
    return res.status(400).json({
      error: 'Validation failed',
      details: errors.array()
    });
  }
  next();
};

// POST /api/auth/register
router.post('/register', 
  endpointLimiters.register,
  validateRegistration,
  handleValidationErrors,
  async (req, res) => {
    try {
      const { email, password, firstName, lastName } = req.body;

      // Check if user already exists
      const existingUser = await User.findOne({ where: { email } });
      if (existingUser) {
        logger.logSecurity('Registration attempt with existing email', { email });
        return res.status(409).json({
          error: 'User already exists',
          message: 'An account with this email address already exists'
        });
      }

      // Hash password
      const saltRounds = 12;
      const hashedPassword = await bcrypt.hash(password, saltRounds);

      // Create user
      const user = await User.create({
        email,
        password: hashedPassword,
        firstName,
        lastName,
        subscriptionTier: 'free',
        isEmailVerified: false
      });

      // Generate tokens
      const accessToken = generateToken(user);
      const refreshTokenValue = generateRefreshToken(user);

      // Log successful registration
      logger.logAuth('User registered', user.id, { email });

      res.status(201).json({
        message: 'User registered successfully',
        user: {
          id: user.id,
          email: user.email,
          firstName: user.firstName,
          lastName: user.lastName,
          subscriptionTier: user.subscriptionTier,
          role: user.role,
          isEmailVerified: user.isEmailVerified
        },
        accessToken,
        refreshToken: refreshTokenValue
      });

    } catch (error) {
      logger.error('Registration error:', error);
      res.status(500).json({
        error: 'Registration failed',
        message: 'Unable to create account. Please try again.'
      });
    }
  }
);

// POST /api/auth/login
router.post('/login',
  validateLogin,
  handleValidationErrors,
  async (req, res) => {
    try {
      const { email, password } = req.body;

      // Find user
      const user = await User.findOne({ where: { email } });
      if (!user) {
        logger.logSecurity('Login attempt with non-existent email', { email });
        return res.status(401).json({
          error: 'Invalid credentials',
          message: 'Email or password is incorrect'
        });
      }

      // Check if account is active
      if (!user.isActive) {
        logger.logSecurity('Login attempt with deactivated account', { email, userId: user.id });
        return res.status(401).json({
          error: 'Account deactivated',
          message: 'Your account has been deactivated. Please contact support.'
        });
      }

      // Verify password
      const isPasswordValid = await bcrypt.compare(password, user.password);
      if (!isPasswordValid) {
        logger.logSecurity('Login attempt with invalid password', { email, userId: user.id });
        return res.status(401).json({
          error: 'Invalid credentials',
          message: 'Email or password is incorrect'
        });
      }

      // Update last login
      await user.update({ lastLoginAt: new Date() });

      // Generate tokens
      const accessToken = generateToken(user);
      const refreshTokenValue = generateRefreshToken(user);

      // Log successful login
      logger.logAuth('User logged in', user.id, { email });

      res.json({
        message: 'Login successful',
        user: {
          id: user.id,
          email: user.email,
          firstName: user.firstName,
          lastName: user.lastName,
          subscriptionTier: user.subscriptionTier,
          role: user.role,
          isEmailVerified: user.isEmailVerified
        },
        accessToken,
        refreshToken: refreshTokenValue
      });

    } catch (error) {
      logger.error('Login error:', error);
      res.status(500).json({
        error: 'Login failed',
        message: 'Unable to log in. Please try again.'
      });
    }
  }
);

// POST /api/auth/refresh
router.post('/refresh', refreshToken);

// POST /api/auth/logout
router.post('/logout', async (req, res) => {
  try {
    // In a more sophisticated implementation, you would:
    // 1. Blacklist the current token
    // 2. Remove refresh token from database
    // 3. Clear any session data
    
    const authHeader = req.headers.authorization;
    const token = authHeader && authHeader.split(' ')[1];
    
    if (token) {
      // Here you would add the token to a blacklist
      // For now, we'll just log the logout
      logger.info('User logged out');
    }

    res.json({
      message: 'Logged out successfully'
    });

  } catch (error) {
    logger.error('Logout error:', error);
    res.status(500).json({
      error: 'Logout failed',
      message: 'Unable to log out. Please try again.'
    });
  }
});

// POST /api/auth/forgot-password
router.post('/forgot-password',
  endpointLimiters.passwordReset,
  validatePasswordReset,
  handleValidationErrors,
  async (req, res) => {
    try {
      const { email } = req.body;

      // Find user
      const user = await User.findOne({ where: { email } });
      
      // Always return success to prevent email enumeration
      // In a real implementation, you would send a password reset email
      logger.logAuth('Password reset requested', user?.id || 'unknown', { email });

      res.json({
        message: 'If an account with that email exists, a password reset link has been sent.'
      });

    } catch (error) {
      logger.error('Password reset error:', error);
      res.status(500).json({
        error: 'Password reset failed',
        message: 'Unable to process password reset. Please try again.'
      });
    }
  }
);

// GET /api/auth/verify-email/:token
router.get('/verify-email/:token', async (req, res) => {
  try {
    const { token } = req.params;

    // In a real implementation, you would:
    // 1. Verify the email verification token
    // 2. Update user's isEmailVerified status
    // 3. Redirect to success page

    res.json({
      message: 'Email verification endpoint - implementation needed'
    });

  } catch (error) {
    logger.error('Email verification error:', error);
    res.status(500).json({
      error: 'Email verification failed',
      message: 'Unable to verify email. Please try again.'
    });
  }
});

// GET /api/auth/me (get current user info)
router.get('/me', async (req, res) => {
  try {
    // This endpoint would typically require authentication
    // For now, return a placeholder response
    res.json({
      message: 'User profile endpoint - requires authentication middleware'
    });

  } catch (error) {
    logger.error('Get user profile error:', error);
    res.status(500).json({
      error: 'Unable to get user profile',
      message: 'Please try again.'
    });
  }
});

module.exports = router;
