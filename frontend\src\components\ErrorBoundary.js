import React from 'react';
import { Al<PERSON><PERSON>riangle, RefreshCw, Home, Bug } from 'lucide-react';
import { motion } from 'framer-motion';

class ErrorBoundary extends React.Component {
  constructor(props) {
    super(props);
    this.state = { 
      hasError: false, 
      error: null, 
      errorInfo: null,
      errorId: null
    };
  }

  static getDerivedStateFromError(error) {
    // Update state so the next render will show the fallback UI
    return { 
      hasError: true,
      errorId: Date.now().toString(36) + Math.random().toString(36).substr(2)
    };
  }

  componentDidCatch(error, errorInfo) {
    // Log error details
    console.error('ErrorBoundary caught an error:', error, errorInfo);
    
    this.setState({
      error: error,
      errorInfo: errorInfo
    });

    // Report error to monitoring service (if available)
    if (window.gtag) {
      window.gtag('event', 'exception', {
        description: error.toString(),
        fatal: true
      });
    }

    // Send error to backend for logging
    if (process.env.NODE_ENV === 'production') {
      this.reportError(error, errorInfo);
    }
  }

  reportError = async (error, errorInfo) => {
    try {
      await fetch('/api/errors', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          message: error.message,
          stack: error.stack,
          componentStack: errorInfo.componentStack,
          errorId: this.state.errorId,
          url: window.location.href,
          userAgent: navigator.userAgent,
          timestamp: new Date().toISOString()
        })
      });
    } catch (reportingError) {
      console.error('Failed to report error:', reportingError);
    }
  };

  handleReload = () => {
    window.location.reload();
  };

  handleGoHome = () => {
    window.location.href = '/';
  };

  handleRetry = () => {
    this.setState({ 
      hasError: false, 
      error: null, 
      errorInfo: null,
      errorId: null 
    });
  };

  render() {
    if (this.state.hasError) {
      const isDevelopment = process.env.NODE_ENV === 'development';
      
      return (
        <div className="min-h-screen bg-black text-white font-mono flex items-center justify-center p-4">
          <motion.div
            initial={{ opacity: 0, scale: 0.9 }}
            animate={{ opacity: 1, scale: 1 }}
            transition={{ duration: 0.5 }}
            className="max-w-2xl w-full"
          >
            {/* Error Icon */}
            <div className="text-center mb-8">
              <motion.div
                animate={{ rotate: [0, -10, 10, -10, 0] }}
                transition={{ duration: 0.5, repeat: 3 }}
                className="inline-block"
              >
                <div className="w-24 h-24 bg-[#FF0066] border-4 border-white mx-auto flex items-center justify-center transform rotate-12 shadow-[8px_8px_0px_0px_#000000]">
                  <AlertTriangle className="w-12 h-12 text-white" />
                </div>
              </motion.div>
            </div>

            {/* Error Title */}
            <div className="text-center mb-8">
              <h1 className="text-4xl font-black mb-4 transform -rotate-1">
                OOPS! SOMETHING BROKE
              </h1>
              <p className="text-xl font-bold text-[#FF0066] transform rotate-1">
                THE BRUTAL TRUTH: WE MESSED UP
              </p>
            </div>

            {/* Error Message */}
            <div className="bg-[#FF0066] border-4 border-white p-6 mb-8 shadow-[6px_6px_0px_0px_#000000] transform -rotate-1">
              <h2 className="text-xl font-black text-white mb-4 flex items-center">
                <Bug className="w-6 h-6 mr-2" />
                ERROR DETAILS
              </h2>
              <div className="bg-black border-2 border-white p-4 font-mono text-sm">
                <p className="text-[#00FF66] mb-2">
                  <strong>Error ID:</strong> {this.state.errorId}
                </p>
                <p className="text-white mb-2">
                  <strong>Message:</strong> {this.state.error?.message || 'Unknown error occurred'}
                </p>
                <p className="text-[#FFFF00]">
                  <strong>Time:</strong> {new Date().toLocaleString()}
                </p>
              </div>
            </div>

            {/* Development Error Details */}
            {isDevelopment && this.state.error && (
              <div className="bg-[#0066FF] border-4 border-white p-6 mb-8 shadow-[6px_6px_0px_0px_#000000] transform rotate-1">
                <h3 className="text-lg font-black text-white mb-4">
                  DEVELOPMENT INFO
                </h3>
                <div className="bg-black border-2 border-white p-4 font-mono text-xs overflow-auto max-h-40">
                  <pre className="text-[#FF0066] whitespace-pre-wrap">
                    {this.state.error.stack}
                  </pre>
                  {this.state.errorInfo && (
                    <pre className="text-[#00FF66] whitespace-pre-wrap mt-4">
                      {this.state.errorInfo.componentStack}
                    </pre>
                  )}
                </div>
              </div>
            )}

            {/* Action Buttons */}
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <motion.button
                whileHover={{ scale: 1.05, rotate: 1 }}
                whileTap={{ scale: 0.95 }}
                onClick={this.handleRetry}
                className="bg-[#00FF66] text-black font-black py-4 px-8 border-4 border-white shadow-[4px_4px_0px_0px_#000000] hover:shadow-[2px_2px_0px_0px_#000000] transition-all flex items-center justify-center"
              >
                <RefreshCw className="w-5 h-5 mr-2" />
                TRY AGAIN
              </motion.button>

              <motion.button
                whileHover={{ scale: 1.05, rotate: -1 }}
                whileTap={{ scale: 0.95 }}
                onClick={this.handleReload}
                className="bg-[#0066FF] text-white font-black py-4 px-8 border-4 border-white shadow-[4px_4px_0px_0px_#000000] hover:shadow-[2px_2px_0px_0px_#000000] transition-all flex items-center justify-center"
              >
                <RefreshCw className="w-5 h-5 mr-2" />
                RELOAD PAGE
              </motion.button>

              <motion.button
                whileHover={{ scale: 1.05, rotate: 1 }}
                whileTap={{ scale: 0.95 }}
                onClick={this.handleGoHome}
                className="bg-[#FFFF00] text-black font-black py-4 px-8 border-4 border-white shadow-[4px_4px_0px_0px_#000000] hover:shadow-[2px_2px_0px_0px_#000000] transition-all flex items-center justify-center"
              >
                <Home className="w-5 h-5 mr-2" />
                GO HOME
              </motion.button>
            </div>

            {/* Help Text */}
            <div className="text-center mt-8">
              <p className="text-gray-400 font-bold">
                If this keeps happening, contact our support team with Error ID: 
                <span className="text-[#00FF66] ml-1">{this.state.errorId}</span>
              </p>
            </div>

            {/* Background Animation */}
            <div className="fixed inset-0 pointer-events-none overflow-hidden -z-10">
              {[...Array(10)].map((_, i) => (
                <motion.div
                  key={i}
                  initial={{ 
                    x: Math.random() * window.innerWidth,
                    y: Math.random() * window.innerHeight,
                    opacity: 0
                  }}
                  animate={{ 
                    x: Math.random() * window.innerWidth,
                    y: Math.random() * window.innerHeight,
                    opacity: [0, 0.1, 0]
                  }}
                  transition={{
                    duration: Math.random() * 20 + 10,
                    repeat: Infinity,
                    delay: Math.random() * 5
                  }}
                  className="absolute w-4 h-4 bg-[#FF0066] border border-white"
                  style={{
                    transform: `rotate(${Math.random() * 360}deg)`
                  }}
                />
              ))}
            </div>
          </motion.div>
        </div>
      );
    }

    return this.props.children;
  }
}

export default ErrorBoundary;
