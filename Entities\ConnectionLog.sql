{"name": "ConnectionLog", "type": "object", "properties": {"country": {"type": "string", "description": "Connected country"}, "ip_address": {"type": "string", "description": "Assigned IP address"}, "connection_start": {"type": "string", "format": "date-time", "description": "When connection started"}, "connection_end": {"type": "string", "format": "date-time", "description": "When connection ended"}, "duration_seconds": {"type": "number", "description": "Connection duration in seconds"}, "bytes_transferred": {"type": "number", "description": "Total bytes transferred"}, "average_speed": {"type": "number", "description": "Average connection speed in Mbps"}, "disconnect_reason": {"type": "string", "enum": ["user", "auto_rotation", "connection_lost", "error"], "description": "Why the connection ended"}, "server_location": {"type": "string", "description": "Physical server location"}, "protocol_used": {"type": "string", "enum": ["openvpn", "wireguard", "ikev2"], "description": "VPN protocol used"}}, "required": ["country", "ip_address", "connection_start"]}