{"name": "proxyforge-backend", "version": "1.0.0", "description": "ProxyForge Backend API Server", "main": "src/server.js", "scripts": {"start": "node src/server.js", "dev": "nodemon src/server.js", "test": "jest", "test:watch": "jest --watch", "build": "npm run clean && npm run compile", "clean": "<PERSON><PERSON><PERSON> dist", "compile": "babel src -d dist", "lint": "eslint src/", "lint:fix": "eslint src/ --fix"}, "keywords": ["proxy", "vpn", "nodejs", "express", "api"], "author": "HectorTa1989", "license": "MIT", "dependencies": {"express": "^4.18.2", "express-rate-limit": "^7.1.5", "express-validator": "^7.0.1", "cors": "^2.8.5", "helmet": "^7.1.0", "compression": "^1.7.4", "morgan": "^1.10.0", "winston": "^3.11.0", "jsonwebtoken": "^9.0.2", "bcryptjs": "^2.4.3", "passport": "^0.7.0", "passport-jwt": "^4.0.1", "passport-local": "^1.0.0", "sequelize": "^6.35.2", "sqlite3": "^5.1.6", "pg": "^8.11.3", "redis": "^4.6.10", "ioredis": "^5.3.2", "http-proxy-middleware": "^2.0.6", "socks": "^2.7.1", "axios": "^1.6.2", "node-cron": "^3.0.3", "uuid": "^9.0.1", "dotenv": "^16.3.1", "joi": "^17.11.0", "multer": "^1.4.5-lts.1", "sharp": "^0.33.1", "nodemailer": "^6.9.7", "socket.io": "^4.7.4", "ws": "^8.14.2"}, "devDependencies": {"nodemon": "^3.0.2", "jest": "^29.7.0", "supertest": "^6.3.3", "eslint": "^8.55.0", "eslint-config-airbnb-base": "^15.0.0", "eslint-plugin-import": "^2.29.0", "@babel/cli": "^7.23.4", "@babel/core": "^7.23.6", "@babel/preset-env": "^7.23.6", "rimraf": "^5.0.5"}, "engines": {"node": ">=18.0.0", "npm": ">=8.0.0"}, "repository": {"type": "git", "url": "https://github.com/HectorTa1989/proxyforge.git"}, "bugs": {"url": "https://github.com/HectorTa1989/proxyforge/issues"}, "homepage": "https://github.com/HectorTa1989/proxyforge#readme"}