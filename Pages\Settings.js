import React, { useState } from 'react';
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON><PERSON><PERSON>, Card<PERSON>itle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Switch } from "@/components/ui/switch";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Badge } from "@/components/ui/badge";
import { Settings as SettingsIcon, Shield, Zap, Globe, AlertTriangle, Save, RotateCcw } from 'lucide-react';
import { motion } from 'framer-motion';

export default function Settings() {
  const [settings, setSettings] = useState({
    autoConnect: true,
    killSwitch: true,
    dnsLeakProtection: true,
    webRTCBlocking: false,
    autoRotation: true,
    rotationInterval: 30,
    protocol: 'openvpn',
    encryption: 'aes256',
    customDNS: '',
    startMinimized: false,
    notifications: true,
    theme: 'brutal'
  });

  const [hasChanges, setHasChanges] = useState(false);

  const updateSetting = (key, value) => {
    setSettings(prev => ({ ...prev, [key]: value }));
    setHasChanges(true);
  };

  const saveSettings = () => {
    // Save settings logic here
    setHasChanges(false);
    console.log('Settings saved:', settings);
  };

  const resetSettings = () => {
    setSettings({
      autoConnect: true,
      killSwitch: true,
      dnsLeakProtection: true,
      webRTCBlocking: false,
      autoRotation: true,
      rotationInterval: 30,
      protocol: 'openvpn',
      encryption: 'aes256',
      customDNS: '',
      startMinimized: false,
      notifications: true,
      theme: 'brutal'
    });
    setHasChanges(true);
  };

  return (
    <div className="min-h-screen bg-white p-6 font-mono">
      <div className="max-w-5xl mx-auto">
        {/* Header */}
        <div className="mb-8">
          <h1 className="text-5xl font-black text-black mb-2 transform rotate-1">
            BRUTAL SETTINGS
          </h1>
          <p className="text-xl text-black font-bold">
            CONFIGURE YOUR EXPERIENCE
          </p>
        </div>

        {/* Save/Reset Controls */}
        {hasChanges && (
          <motion.div
            initial={{ opacity: 0, y: -20 }}
            animate={{ opacity: 1, y: 0 }}
            className="mb-8"
          >
            <Card className="border-4 border-black shadow-[8px_8px_0px_0px_#FF0066] bg-[#FFFF00] transform -rotate-1">
              <CardContent className="p-4">
                <div className="flex items-center justify-between">
                  <div className="flex items-center gap-2">
                    <AlertTriangle className="w-6 h-6 text-black" />
                    <span className="font-black text-lg">UNSAVED CHANGES</span>
                  </div>
                  <div className="flex gap-4">
                    <Button
                      onClick={resetSettings}
                      className="font-black border-4 border-black shadow-[4px_4px_0px_0px_#000000] bg-[#FF0066] text-white hover:bg-[#FF0066]/90"
                    >
                      <RotateCcw className="w-4 h-4 mr-2" />
                      RESET
                    </Button>
                    <Button
                      onClick={saveSettings}
                      className="font-black border-4 border-black shadow-[4px_4px_0px_0px_#000000] bg-[#00FF66] text-black hover:bg-[#00FF66]/90"
                    >
                      <Save className="w-4 h-4 mr-2" />
                      SAVE NOW
                    </Button>
                  </div>
                </div>
              </CardContent>
            </Card>
          </motion.div>
        )}

        {/* Settings Grid */}
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
          {/* Connection Settings */}
          <Card className="border-4 border-black shadow-[8px_8px_0px_0px_#0066FF] bg-white transform rotate-1">
            <CardHeader className="border-b-4 border-black bg-[#0066FF] text-white">
              <CardTitle className="text-2xl font-black flex items-center gap-2">
                <Globe className="w-6 h-6" />
                CONNECTION
              </CardTitle>
            </CardHeader>
            <CardContent className="p-6 space-y-6">
              <div className="flex items-center justify-between">
                <div>
                  <Label className="text-lg font-black">AUTO CONNECT</Label>
                  <p className="text-sm text-[#666666] font-bold">Connect automatically on startup</p>
                </div>
                <Switch
                  checked={settings.autoConnect}
                  onCheckedChange={(value) => updateSetting('autoConnect', value)}
                  className="data-[state=checked]:bg-[#00FF66] border-4 border-black"
                />
              </div>

              <div className="flex items-center justify-between">
                <div>
                  <Label className="text-lg font-black">AUTO ROTATION</Label>
                  <p className="text-sm text-[#666666] font-bold">Automatically switch locations</p>
                </div>
                <Switch
                  checked={settings.autoRotation}
                  onCheckedChange={(value) => updateSetting('autoRotation', value)}
                  className="data-[state=checked]:bg-[#00FF66] border-4 border-black"
                />
              </div>

              {settings.autoRotation && (
                <motion.div
                  initial={{ opacity: 0, height: 0 }}
                  animate={{ opacity: 1, height: 'auto' }}
                  className="space-y-2"
                >
                  <Label className="text-lg font-black">ROTATION INTERVAL</Label>
                  <Select
                    value={settings.rotationInterval.toString()}
                    onValueChange={(value) => updateSetting('rotationInterval', parseInt(value))}
                  >
                    <SelectTrigger className="border-4 border-black text-lg font-bold h-12">
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="15">15 MINUTES</SelectItem>
                      <SelectItem value="30">30 MINUTES</SelectItem>
                      <SelectItem value="60">1 HOUR</SelectItem>
                      <SelectItem value="120">2 HOURS</SelectItem>
                    </SelectContent>
                  </Select>
                </motion.div>
              )}

              <div className="space-y-2">
                <Label className="text-lg font-black">PROTOCOL</Label>
                <Select
                  value={settings.protocol}
                  onValueChange={(value) => updateSetting('protocol', value)}
                >
                  <SelectTrigger className="border-4 border-black text-lg font-bold h-12">
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="openvpn">OPENVPN</SelectItem>
                    <SelectItem value="wireguard">WIREGUARD</SelectItem>
                    <SelectItem value="ikev2">IKEV2</SelectItem>
                  </SelectContent>
                </Select>
              </div>
            </CardContent>
          </Card>

          {/* Security Settings */}
          <Card className="border-4 border-black shadow-[8px_8px_0px_0px_#FF0066] bg-white transform -rotate-1">
            <CardHeader className="border-b-4 border-black bg-[#FF0066] text-white">
              <CardTitle className="text-2xl font-black flex items-center gap-2">
                <Shield className="w-6 h-6" />
                SECURITY
              </CardTitle>
            </CardHeader>
            <CardContent className="p-6 space-y-6">
              <div className="flex items-center justify-between">
                <div>
                  <Label className="text-lg font-black">KILL SWITCH</Label>
                  <p className="text-sm text-[#666666] font-bold">Block internet if VPN disconnects</p>
                </div>
                <Switch
                  checked={settings.killSwitch}
                  onCheckedChange={(value) => updateSetting('killSwitch', value)}
                  className="data-[state=checked]:bg-[#00FF66] border-4 border-black"
                />
              </div>

              <div className="flex items-center justify-between">
                <div>
                  <Label className="text-lg font-black">DNS LEAK PROTECTION</Label>
                  <p className="text-sm text-[#666666] font-bold">Prevent DNS requests from leaking</p>
                </div>
                <Switch
                  checked={settings.dnsLeakProtection}
                  onCheckedChange={(value) => updateSetting('dnsLeakProtection', value)}
                  className="data-[state=checked]:bg-[#00FF66] border-4 border-black"
                />
              </div>

              <div className="flex items-center justify-between">
                <div>
                  <Label className="text-lg font-black">WEBRTC BLOCKING</Label>
                  <p className="text-sm text-[#666666] font-bold">Block WebRTC IP leaks</p>
                </div>
                <Switch
                  checked={settings.webRTCBlocking}
                  onCheckedChange={(value) => updateSetting('webRTCBlocking', value)}
                  className="data-[state=checked]:bg-[#00FF66] border-4 border-black"
                />
              </div>

              <div className="space-y-2">
                <Label className="text-lg font-black">ENCRYPTION</Label>
                <Select
                  value={settings.encryption}
                  onValueChange={(value) => updateSetting('encryption', value)}
                >
                  <SelectTrigger className="border-4 border-black text-lg font-bold h-12">
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="aes256">AES-256</SelectItem>
                    <SelectItem value="aes128">AES-128</SelectItem>
                    <SelectItem value="chacha20">CHACHA20</SelectItem>
                  </SelectContent>
                </Select>
              </div>

              <div className="space-y-2">
                <Label className="text-lg font-black">CUSTOM DNS</Label>
                <Input
                  placeholder="8.8.8.8, 1.1.1.1"
                  value={settings.customDNS}
                  onChange={(e) => updateSetting('customDNS', e.target.value)}
                  className="border-4 border-black text-lg font-bold h-12"
                />
              </div>
            </CardContent>
          </Card>

          {/* Performance Settings */}
          <Card className="border-4 border-black shadow-[8px_8px_0px_0px_#00FF66] bg-white transform rotate-0.5">
            <CardHeader className="border-b-4 border-black bg-[#00FF66] text-black">
              <CardTitle className="text-2xl font-black flex items-center gap-2">
                <Zap className="w-6 h-6" />
                PERFORMANCE
              </CardTitle>
            </CardHeader>
            <CardContent className="p-6 space-y-6">
              <div className="space-y-2">
                <Label className="text-lg font-black">CONNECTION PRIORITY</Label>
                <div className="grid grid-cols-3 gap-2">
                  {['SPEED', 'SECURITY', 'BALANCED'].map((priority) => (
                    <Button
                      key={priority}
                      className={`font-black border-4 border-black shadow-[4px_4px_0px_0px_#000000] ${
                        settings.priority === priority.toLowerCase()
                          ? 'bg-[#FFFF00] text-black'
                          : 'bg-white text-black hover:bg-[#FFFF00]'
                      }`}
                      onClick={() => updateSetting('priority', priority.toLowerCase())}
                    >
                      {priority}
                    </Button>
                  ))}
                </div>
              </div>

              <div className="p-4 border-4 border-black bg-black text-white">
                <h3 className="font-black text-lg mb-2 text-[#00FF66]">CURRENT STATUS</h3>
                <div className="space-y-2 text-sm font-bold">
                  <div className="flex justify-between">
                    <span>ENCRYPTION:</span>
                    <Badge className="bg-[#00FF66] text-black border-2 border-white">
                      {settings.encryption.toUpperCase()}
                    </Badge>
                  </div>
                  <div className="flex justify-between">
                    <span>PROTOCOL:</span>
                    <Badge className="bg-[#0066FF] text-white border-2 border-white">
                      {settings.protocol.toUpperCase()}
                    </Badge>
                  </div>
                  <div className="flex justify-between">
                    <span>KILL SWITCH:</span>
                    <Badge className={settings.killSwitch ? "bg-[#00FF66] text-black" : "bg-[#FF0066] text-white"}>
                      {settings.killSwitch ? 'ENABLED' : 'DISABLED'}
                    </Badge>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Application Settings */}
          <Card className="border-4 border-black shadow-[8px_8px_0px_0px_#FFFF00] bg-white transform -rotate-0.5">
            <CardHeader className="border-b-4 border-black bg-[#FFFF00] text-black">
              <CardTitle className="text-2xl font-black flex items-center gap-2">
                <SettingsIcon className="w-6 h-6" />
                APPLICATION
              </CardTitle>
            </CardHeader>
            <CardContent className="p-6 space-y-6">
              <div className="flex items-center justify-between">
                <div>
                  <Label className="text-lg font-black">START MINIMIZED</Label>
                  <p className="text-sm text-[#666666] font-bold">Start app in system tray</p>
                </div>
                <Switch
                  checked={settings.startMinimized}
                  onCheckedChange={(value) => updateSetting('startMinimized', value)}
                  className="data-[state=checked]:bg-[#00FF66] border-4 border-black"
                />
              </div>

              <div className="flex items-center justify-between">
                <div>
                  <Label className="text-lg font-black">NOTIFICATIONS</Label>
                  <p className="text-sm text-[#666666] font-bold">Show connection status alerts</p>
                </div>
                <Switch
                  checked={settings.notifications}
                  onCheckedChange={(value) => updateSetting('notifications', value)}
                  className="data-[state=checked]:bg-[#00FF66] border-4 border-black"
                />
              </div>

              <div className="space-y-2">
                <Label className="text-lg font-black">THEME</Label>
                <Select
                  value={settings.theme}
                  onValueChange={(value) => updateSetting('theme', value)}
                >
                  <SelectTrigger className="border-4 border-black text-lg font-bold h-12">
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="brutal">BRUTAL</SelectItem>
                    <SelectItem value="dark">DARK</SelectItem>
                    <SelectItem value="light">LIGHT</SelectItem>
                  </SelectContent>
                </Select>
              </div>

              <div className="p-4 border-4 border-black bg-[#FF0066] text-white">
                <h3 className="font-black text-lg mb-2">DANGER ZONE</h3>
                <Button
                  className="w-full font-black border-4 border-white shadow-[4px_4px_0px_0px_#000000] bg-black text-white hover:bg-[#333333]"
                  onClick={() => {
                    // Reset all data logic here
                    console.log('Reset all data');
                  }}
                >
                  RESET ALL DATA
                </Button>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  );
}