const express = require('express');
const { body, query, validationResult } = require('express-validator');
const { ProxyServer, Connection } = require('../models');
const { proxyLimiter, createUserBasedLimiter } = require('../middleware/rateLimiter');
const { requireSubscription } = require('../middleware/auth');
const ProxyService = require('../services/proxyService');
const GeoService = require('../services/geoService');
const logger = require('../utils/logger');

const router = express.Router();

// Initialize services
const proxyService = new ProxyService();
const geoService = new GeoService();

// Validation middleware
const validateCountryCode = [
  query('country')
    .optional()
    .isLength({ min: 2, max: 2 })
    .isAlpha()
    .toUpperCase()
    .withMessage('Country code must be a 2-letter ISO code')
];

const validateConnectionRequest = [
  body('country')
    .optional()
    .isLength({ min: 2, max: 2 })
    .isAlpha()
    .toUpperCase()
    .withMessage('Country code must be a 2-letter ISO code'),
  body('proxyType')
    .optional()
    .isIn(['http', 'https', 'socks4', 'socks5'])
    .withMessage('Invalid proxy type'),
  body('autoRotate')
    .optional()
    .isBoolean()
    .withMessage('autoRotate must be a boolean')
];

// Helper function to handle validation errors
const handleValidationErrors = (req, res, next) => {
  const errors = validationResult(req);
  if (!errors.isEmpty()) {
    return res.status(400).json({
      error: 'Validation failed',
      details: errors.array()
    });
  }
  next();
};

// GET /api/proxy/servers - Get available proxy servers
router.get('/servers',
  createUserBasedLimiter(20), // 20 requests per 15 minutes for free users
  validateCountryCode,
  handleValidationErrors,
  async (req, res) => {
    try {
      const { country, type, minSpeed, minReliability } = req.query;
      const userId = req.user.id;

      // Build filter criteria
      const filters = {
        isActive: true
      };

      if (country) {
        filters.countryCode = country;
      }

      if (type) {
        filters.type = type;
      }

      if (minSpeed) {
        filters.speed = { [ProxyServer.sequelize.Op.gte]: parseInt(minSpeed) };
      }

      if (minReliability) {
        filters.reliability = { [ProxyServer.sequelize.Op.gte]: parseFloat(minReliability) };
      }

      // Get proxy servers from database
      const proxyServers = await ProxyServer.findAll({
        where: filters,
        order: [
          ['reliability', 'DESC'],
          ['speed', 'DESC']
        ],
        limit: req.user.subscriptionTier === 'free' ? 10 : 100
      });

      // Get additional proxies from proxy service
      const additionalProxies = await proxyService.getProxyList(country);

      // Combine and format results
      const allProxies = [
        ...proxyServers.map(server => ({
          id: server.id,
          host: server.host,
          port: server.port,
          type: server.type,
          country: server.country,
          countryCode: server.countryCode,
          city: server.city,
          speed: server.speed,
          reliability: server.reliability,
          latency: server.latency,
          source: 'database'
        })),
        ...additionalProxies.slice(0, req.user.subscriptionTier === 'free' ? 5 : 50)
      ];

      logger.info(`Proxy servers requested by user ${userId}: ${allProxies.length} servers returned`);

      res.json({
        servers: allProxies,
        total: allProxies.length,
        userTier: req.user.subscriptionTier,
        filters: {
          country,
          type,
          minSpeed,
          minReliability
        }
      });

    } catch (error) {
      logger.error('Error fetching proxy servers:', error);
      res.status(500).json({
        error: 'Unable to fetch proxy servers',
        message: 'Please try again later'
      });
    }
  }
);

// POST /api/proxy/connect - Establish proxy connection
router.post('/connect',
  proxyLimiter,
  validateConnectionRequest,
  handleValidationErrors,
  async (req, res) => {
    try {
      const { country, proxyType = 'http', autoRotate = false } = req.body;
      const userId = req.user.id;

      // Get available proxy based on criteria
      let selectedProxy;
      
      if (country) {
        const countryProxies = await proxyService.getProxyList(country);
        selectedProxy = countryProxies.find(p => p.type === proxyType) || countryProxies[0];
      } else {
        selectedProxy = proxyService.getNextProxy();
      }

      if (!selectedProxy) {
        return res.status(503).json({
          error: 'No proxy servers available',
          message: 'Please try again later or select a different location'
        });
      }

      // Get geo information for the proxy
      const geoInfo = await geoService.getLocationInfo(selectedProxy.host);

      // Log the connection attempt
      await Connection.create({
        userId,
        proxyHost: selectedProxy.host,
        proxyPort: selectedProxy.port,
        targetUrl: 'connection-established',
        method: 'CONNECT',
        statusCode: 200,
        metadata: {
          country,
          proxyType,
          autoRotate,
          geoInfo
        }
      });

      logger.logProxy(userId, selectedProxy.host, 'connection-established', 200, 0);

      res.json({
        success: true,
        connection: {
          id: selectedProxy.id,
          proxyHost: selectedProxy.host,
          proxyPort: selectedProxy.port,
          proxyType: selectedProxy.type,
          country: geoInfo.country || selectedProxy.country,
          countryCode: geoInfo.countryCode || selectedProxy.countryCode,
          city: geoInfo.city || selectedProxy.city,
          speed: selectedProxy.speed,
          reliability: selectedProxy.reliability,
          autoRotate
        },
        proxyUrl: `http://localhost:${process.env.PROXY_PORT || 8080}`,
        instructions: {
          http: `Set your HTTP proxy to localhost:${process.env.PROXY_PORT || 8080}`,
          https: `Set your HTTPS proxy to localhost:${process.env.PROXY_PORT || 8080}`,
          authentication: 'Include your access token in the X-User-ID header'
        }
      });

    } catch (error) {
      logger.error('Error establishing proxy connection:', error);
      res.status(500).json({
        error: 'Connection failed',
        message: 'Unable to establish proxy connection'
      });
    }
  }
);

// POST /api/proxy/disconnect - Disconnect from proxy
router.post('/disconnect', async (req, res) => {
  try {
    const userId = req.user.id;

    // In a more sophisticated implementation, you would:
    // 1. Close active proxy connections for the user
    // 2. Clean up any resources
    // 3. Update connection logs

    logger.logAuth('Proxy disconnected', userId);

    res.json({
      success: true,
      message: 'Disconnected from proxy server'
    });

  } catch (error) {
    logger.error('Error disconnecting from proxy:', error);
    res.status(500).json({
      error: 'Disconnect failed',
      message: 'Unable to disconnect from proxy'
    });
  }
});

// GET /api/proxy/stats - Get user's proxy usage statistics
router.get('/stats',
  createUserBasedLimiter(30),
  async (req, res) => {
    try {
      const userId = req.user.id;
      const { period = '24h' } = req.query;

      // Calculate date range based on period
      const now = new Date();
      let startDate;
      
      switch (period) {
        case '1h':
          startDate = new Date(now.getTime() - 60 * 60 * 1000);
          break;
        case '24h':
          startDate = new Date(now.getTime() - 24 * 60 * 60 * 1000);
          break;
        case '7d':
          startDate = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000);
          break;
        case '30d':
          startDate = new Date(now.getTime() - 30 * 24 * 60 * 60 * 1000);
          break;
        default:
          startDate = new Date(now.getTime() - 24 * 60 * 60 * 1000);
      }

      // Get connection statistics
      const stats = await Connection.findAll({
        where: {
          userId,
          createdAt: {
            [Connection.sequelize.Op.gte]: startDate
          }
        },
        attributes: [
          [Connection.sequelize.fn('COUNT', '*'), 'totalConnections'],
          [Connection.sequelize.fn('AVG', Connection.sequelize.col('responseTime')), 'avgResponseTime'],
          [Connection.sequelize.fn('SUM', Connection.sequelize.col('bytesTransferred')), 'totalBytes'],
          [Connection.sequelize.fn('COUNT', Connection.sequelize.literal('CASE WHEN statusCode >= 200 AND statusCode < 300 THEN 1 END')), 'successfulConnections']
        ],
        raw: true
      });

      // Get top countries used
      const topCountries = await Connection.findAll({
        where: {
          userId,
          createdAt: {
            [Connection.sequelize.Op.gte]: startDate
          }
        },
        attributes: [
          'proxyHost',
          [Connection.sequelize.fn('COUNT', '*'), 'count']
        ],
        group: ['proxyHost'],
        order: [[Connection.sequelize.literal('count'), 'DESC']],
        limit: 5,
        raw: true
      });

      const result = {
        period,
        totalConnections: parseInt(stats[0].totalConnections) || 0,
        avgResponseTime: Math.round(parseFloat(stats[0].avgResponseTime)) || 0,
        totalBytesTransferred: parseInt(stats[0].totalBytes) || 0,
        successRate: stats[0].totalConnections > 0 
          ? Math.round((stats[0].successfulConnections / stats[0].totalConnections) * 100)
          : 0,
        topProxies: topCountries.map(country => ({
          host: country.proxyHost,
          connections: parseInt(country.count)
        })),
        subscriptionTier: req.user.subscriptionTier
      };

      res.json(result);

    } catch (error) {
      logger.error('Error fetching proxy stats:', error);
      res.status(500).json({
        error: 'Unable to fetch statistics',
        message: 'Please try again later'
      });
    }
  }
);

// GET /api/proxy/countries - Get available countries
router.get('/countries',
  createUserBasedLimiter(10),
  async (req, res) => {
    try {
      // Get unique countries from proxy servers
      const countries = await ProxyServer.findAll({
        where: { isActive: true },
        attributes: [
          'country',
          'countryCode',
          [ProxyServer.sequelize.fn('COUNT', '*'), 'serverCount'],
          [ProxyServer.sequelize.fn('AVG', ProxyServer.sequelize.col('speed')), 'avgSpeed'],
          [ProxyServer.sequelize.fn('AVG', ProxyServer.sequelize.col('reliability')), 'avgReliability']
        ],
        group: ['country', 'countryCode'],
        order: [['country', 'ASC']],
        raw: true
      });

      // Add additional countries from proxy service
      const additionalCountries = await geoService.getSupportedCountries();

      // Combine and deduplicate
      const allCountries = new Map();
      
      countries.forEach(country => {
        allCountries.set(country.countryCode, {
          name: country.country,
          code: country.countryCode,
          serverCount: parseInt(country.serverCount),
          avgSpeed: Math.round(parseFloat(country.avgSpeed)),
          avgReliability: parseFloat(country.avgReliability).toFixed(2),
          flag: geoService.getCountryFlag(country.countryCode)
        });
      });

      additionalCountries.forEach(country => {
        if (!allCountries.has(country.code)) {
          allCountries.set(country.code, {
            name: country.name,
            code: country.code,
            serverCount: 1,
            avgSpeed: 50,
            avgReliability: '0.70',
            flag: country.flag
          });
        }
      });

      const result = Array.from(allCountries.values()).sort((a, b) => a.name.localeCompare(b.name));

      res.json({
        countries: result,
        total: result.length
      });

    } catch (error) {
      logger.error('Error fetching countries:', error);
      res.status(500).json({
        error: 'Unable to fetch countries',
        message: 'Please try again later'
      });
    }
  }
);

// GET /api/proxy/test/:proxyId - Test a specific proxy server
router.get('/test/:proxyId',
  requireSubscription('pro', 'business', 'enterprise'),
  createUserBasedLimiter(5), // Limited testing for performance
  async (req, res) => {
    try {
      const { proxyId } = req.params;
      const userId = req.user.id;

      // Find the proxy server
      const proxyServer = await ProxyServer.findByPk(proxyId);
      if (!proxyServer) {
        return res.status(404).json({
          error: 'Proxy server not found',
          message: 'The specified proxy server does not exist'
        });
      }

      // Test the proxy
      const startTime = Date.now();
      const isWorking = await proxyService.testProxy({
        host: proxyServer.host,
        port: proxyServer.port,
        type: proxyServer.type
      });
      const responseTime = Date.now() - startTime;

      // Update proxy server stats
      await proxyServer.update({
        lastCheckedAt: new Date(),
        latency: responseTime,
        isActive: isWorking
      });

      logger.info(`Proxy test by user ${userId}: ${proxyServer.host}:${proxyServer.port} - ${isWorking ? 'PASS' : 'FAIL'} (${responseTime}ms)`);

      res.json({
        proxyId,
        host: proxyServer.host,
        port: proxyServer.port,
        isWorking,
        responseTime,
        testedAt: new Date().toISOString()
      });

    } catch (error) {
      logger.error('Error testing proxy:', error);
      res.status(500).json({
        error: 'Proxy test failed',
        message: 'Unable to test proxy server'
      });
    }
  }
);

module.exports = router;
