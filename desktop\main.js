const { app, <PERSON><PERSON>er<PERSON><PERSON><PERSON>, <PERSON><PERSON>, Tray, ipc<PERSON>ain, dialog, shell, protocol } = require('electron');
const { autoUpdater } = require('electron-updater');
const Store = require('electron-store');
const log = require('electron-log');
const notifier = require('node-notifier');
const path = require('path');
const { spawn } = require('child_process');

// Configure logging
log.transports.file.level = 'info';
log.transports.console.level = 'debug';

// Initialize store for app settings
const store = new Store({
  defaults: {
    windowBounds: { width: 1200, height: 800 },
    startMinimized: false,
    autoStart: false,
    notifications: true,
    theme: 'dark'
  }
});

// Global variables
let mainWindow = null;
let tray = null;
let backendProcess = null;
let isQuitting = false;

// Development mode check
const isDev = process.env.NODE_ENV === 'development';
const FRONTEND_URL = isDev ? 'http://localhost:3000' : `file://${path.join(__dirname, '../frontend/build/index.html')}`;
const BACKEND_PORT = process.env.BACKEND_PORT || 5000;

// Enable live reload for development
if (isDev) {
  require('electron-reload')(__dirname, {
    electron: path.join(__dirname, '..', 'node_modules', '.bin', 'electron'),
    hardResetMethod: 'exit'
  });
}

// App event handlers
app.whenReady().then(() => {
  createMainWindow();
  createTray();
  setupMenu();
  startBackendServer();
  
  if (!isDev) {
    autoUpdater.checkForUpdatesAndNotify();
  }
  
  log.info('ProxyForge Desktop started');
});

app.on('window-all-closed', () => {
  if (process.platform !== 'darwin') {
    app.quit();
  }
});

app.on('activate', () => {
  if (BrowserWindow.getAllWindows().length === 0) {
    createMainWindow();
  }
});

app.on('before-quit', () => {
  isQuitting = true;
  stopBackendServer();
});

// Create main window
function createMainWindow() {
  const bounds = store.get('windowBounds');
  
  mainWindow = new BrowserWindow({
    ...bounds,
    minWidth: 800,
    minHeight: 600,
    webPreferences: {
      nodeIntegration: false,
      contextIsolation: true,
      enableRemoteModule: false,
      preload: path.join(__dirname, 'preload.js')
    },
    icon: path.join(__dirname, 'build', 'icon.png'),
    title: 'ProxyForge - Professional VPN/Proxy Manager',
    titleBarStyle: process.platform === 'darwin' ? 'hiddenInset' : 'default',
    show: !store.get('startMinimized')
  });

  // Load the app
  mainWindow.loadURL(FRONTEND_URL);

  // Open DevTools in development
  if (isDev) {
    mainWindow.webContents.openDevTools();
  }

  // Handle window events
  mainWindow.on('close', (event) => {
    if (!isQuitting && store.get('minimizeToTray', true)) {
      event.preventDefault();
      mainWindow.hide();
      
      if (store.get('notifications')) {
        notifier.notify({
          title: 'ProxyForge',
          message: 'App minimized to system tray',
          icon: path.join(__dirname, 'build', 'icon.png'),
          timeout: 3000
        });
      }
    }
  });

  mainWindow.on('closed', () => {
    mainWindow = null;
  });

  mainWindow.on('resize', () => {
    store.set('windowBounds', mainWindow.getBounds());
  });

  mainWindow.on('move', () => {
    store.set('windowBounds', mainWindow.getBounds());
  });

  // Handle external links
  mainWindow.webContents.setWindowOpenHandler(({ url }) => {
    shell.openExternal(url);
    return { action: 'deny' };
  });

  // Handle navigation
  mainWindow.webContents.on('will-navigate', (event, navigationUrl) => {
    const parsedUrl = new URL(navigationUrl);
    
    if (parsedUrl.origin !== new URL(FRONTEND_URL).origin) {
      event.preventDefault();
      shell.openExternal(navigationUrl);
    }
  });
}

// Create system tray
function createTray() {
  const trayIcon = path.join(__dirname, 'build', process.platform === 'win32' ? 'tray.ico' : 'tray.png');
  tray = new Tray(trayIcon);

  const contextMenu = Menu.buildFromTemplate([
    {
      label: 'Show ProxyForge',
      click: () => {
        if (mainWindow) {
          mainWindow.show();
          mainWindow.focus();
        } else {
          createMainWindow();
        }
      }
    },
    {
      label: 'Quick Connect',
      submenu: [
        { label: 'United States', click: () => quickConnect('US') },
        { label: 'United Kingdom', click: () => quickConnect('GB') },
        { label: 'Germany', click: () => quickConnect('DE') },
        { label: 'Japan', click: () => quickConnect('JP') },
        { type: 'separator' },
        { label: 'Disconnect', click: () => disconnect() }
      ]
    },
    { type: 'separator' },
    {
      label: 'Settings',
      click: () => {
        if (mainWindow) {
          mainWindow.show();
          mainWindow.webContents.send('navigate-to', '/settings');
        }
      }
    },
    {
      label: 'About',
      click: () => {
        dialog.showMessageBox(mainWindow, {
          type: 'info',
          title: 'About ProxyForge',
          message: 'ProxyForge Desktop',
          detail: `Version: ${app.getVersion()}\nProfessional VPN/Proxy Manager\n\nBuilt with Electron and React`,
          icon: path.join(__dirname, 'build', 'icon.png')
        });
      }
    },
    { type: 'separator' },
    {
      label: 'Quit',
      click: () => {
        isQuitting = true;
        app.quit();
      }
    }
  ]);

  tray.setContextMenu(contextMenu);
  tray.setToolTip('ProxyForge - Professional VPN/Proxy Manager');

  tray.on('double-click', () => {
    if (mainWindow) {
      mainWindow.show();
      mainWindow.focus();
    } else {
      createMainWindow();
    }
  });
}

// Setup application menu
function setupMenu() {
  const template = [
    {
      label: 'File',
      submenu: [
        {
          label: 'New Connection',
          accelerator: 'CmdOrCtrl+N',
          click: () => mainWindow?.webContents.send('new-connection')
        },
        { type: 'separator' },
        {
          label: 'Settings',
          accelerator: 'CmdOrCtrl+,',
          click: () => mainWindow?.webContents.send('navigate-to', '/settings')
        },
        { type: 'separator' },
        {
          label: 'Quit',
          accelerator: process.platform === 'darwin' ? 'Cmd+Q' : 'Ctrl+Q',
          click: () => {
            isQuitting = true;
            app.quit();
          }
        }
      ]
    },
    {
      label: 'Connection',
      submenu: [
        {
          label: 'Quick Connect',
          accelerator: 'CmdOrCtrl+Shift+C',
          click: () => quickConnect()
        },
        {
          label: 'Disconnect',
          accelerator: 'CmdOrCtrl+Shift+D',
          click: () => disconnect()
        },
        { type: 'separator' },
        {
          label: 'Speed Test',
          click: () => mainWindow?.webContents.send('speed-test')
        }
      ]
    },
    {
      label: 'View',
      submenu: [
        { role: 'reload' },
        { role: 'forceReload' },
        { role: 'toggleDevTools' },
        { type: 'separator' },
        { role: 'resetZoom' },
        { role: 'zoomIn' },
        { role: 'zoomOut' },
        { type: 'separator' },
        { role: 'togglefullscreen' }
      ]
    },
    {
      label: 'Window',
      submenu: [
        { role: 'minimize' },
        { role: 'close' },
        {
          label: 'Hide to Tray',
          accelerator: 'CmdOrCtrl+H',
          click: () => mainWindow?.hide()
        }
      ]
    },
    {
      label: 'Help',
      submenu: [
        {
          label: 'Documentation',
          click: () => shell.openExternal('https://docs.proxyforge.io')
        },
        {
          label: 'Support',
          click: () => shell.openExternal('https://support.proxyforge.io')
        },
        { type: 'separator' },
        {
          label: 'Check for Updates',
          click: () => autoUpdater.checkForUpdatesAndNotify()
        },
        {
          label: 'About',
          click: () => {
            dialog.showMessageBox(mainWindow, {
              type: 'info',
              title: 'About ProxyForge',
              message: 'ProxyForge Desktop',
              detail: `Version: ${app.getVersion()}\nProfessional VPN/Proxy Manager\n\nBuilt with ❤️ by HectorTa1989`,
              icon: path.join(__dirname, 'build', 'icon.png')
            });
          }
        }
      ]
    }
  ];

  // macOS specific menu adjustments
  if (process.platform === 'darwin') {
    template.unshift({
      label: app.getName(),
      submenu: [
        { role: 'about' },
        { type: 'separator' },
        { role: 'services' },
        { type: 'separator' },
        { role: 'hide' },
        { role: 'hideOthers' },
        { role: 'unhide' },
        { type: 'separator' },
        { role: 'quit' }
      ]
    });
  }

  const menu = Menu.buildFromTemplate(template);
  Menu.setApplicationMenu(menu);
}

// Start backend server
function startBackendServer() {
  if (isDev) {
    log.info('Development mode: Backend server should be started separately');
    return;
  }

  const backendPath = path.join(__dirname, '../backend/dist/server.js');
  
  backendProcess = spawn('node', [backendPath], {
    env: {
      ...process.env,
      NODE_ENV: 'production',
      PORT: BACKEND_PORT,
      DATABASE_PATH: path.join(app.getPath('userData'), 'database.sqlite')
    },
    stdio: ['pipe', 'pipe', 'pipe']
  });

  backendProcess.stdout.on('data', (data) => {
    log.info(`Backend: ${data.toString()}`);
  });

  backendProcess.stderr.on('data', (data) => {
    log.error(`Backend Error: ${data.toString()}`);
  });

  backendProcess.on('close', (code) => {
    log.info(`Backend process exited with code ${code}`);
  });

  log.info('Backend server started');
}

// Stop backend server
function stopBackendServer() {
  if (backendProcess) {
    backendProcess.kill();
    backendProcess = null;
    log.info('Backend server stopped');
  }
}

// Quick connect function
function quickConnect(country = null) {
  if (mainWindow) {
    mainWindow.webContents.send('quick-connect', { country });
    mainWindow.show();
  }
}

// Disconnect function
function disconnect() {
  if (mainWindow) {
    mainWindow.webContents.send('disconnect');
  }
}

// IPC handlers
ipcMain.handle('get-app-version', () => {
  return app.getVersion();
});

ipcMain.handle('get-app-path', (event, name) => {
  return app.getPath(name);
});

ipcMain.handle('show-message-box', async (event, options) => {
  const result = await dialog.showMessageBox(mainWindow, options);
  return result;
});

ipcMain.handle('show-save-dialog', async (event, options) => {
  const result = await dialog.showSaveDialog(mainWindow, options);
  return result;
});

ipcMain.handle('show-open-dialog', async (event, options) => {
  const result = await dialog.showOpenDialog(mainWindow, options);
  return result;
});

ipcMain.handle('store-get', (event, key) => {
  return store.get(key);
});

ipcMain.handle('store-set', (event, key, value) => {
  store.set(key, value);
});

ipcMain.handle('notify', (event, options) => {
  if (store.get('notifications')) {
    notifier.notify({
      ...options,
      icon: path.join(__dirname, 'build', 'icon.png')
    });
  }
});

// Auto updater events
autoUpdater.on('checking-for-update', () => {
  log.info('Checking for update...');
});

autoUpdater.on('update-available', (info) => {
  log.info('Update available.');
  
  if (store.get('notifications')) {
    notifier.notify({
      title: 'ProxyForge Update',
      message: 'A new version is available and will be downloaded in the background.',
      icon: path.join(__dirname, 'build', 'icon.png')
    });
  }
});

autoUpdater.on('update-not-available', (info) => {
  log.info('Update not available.');
});

autoUpdater.on('error', (err) => {
  log.error('Error in auto-updater:', err);
});

autoUpdater.on('download-progress', (progressObj) => {
  let logMessage = `Download speed: ${progressObj.bytesPerSecond}`;
  logMessage += ` - Downloaded ${progressObj.percent}%`;
  logMessage += ` (${progressObj.transferred}/${progressObj.total})`;
  log.info(logMessage);
});

autoUpdater.on('update-downloaded', (info) => {
  log.info('Update downloaded');
  
  dialog.showMessageBox(mainWindow, {
    type: 'info',
    title: 'Update Ready',
    message: 'Update downloaded. The application will restart to apply the update.',
    buttons: ['Restart Now', 'Later']
  }).then((result) => {
    if (result.response === 0) {
      autoUpdater.quitAndInstall();
    }
  });
});

module.exports = { mainWindow, store };
