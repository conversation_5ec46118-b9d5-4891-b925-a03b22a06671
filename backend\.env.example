# Environment Configuration
NODE_ENV=development
PORT=5000
PROXY_PORT=8080

# Database Configuration
DATABASE_URL=sqlite:./database/development.sqlite
# For PostgreSQL: postgresql://username:password@localhost:5432/proxyforge
DATABASE_SSL=false

# Redis Configuration (optional)
REDIS_URL=redis://localhost:6379
# REDIS_PASSWORD=your_redis_password

# JWT Configuration
JWT_SECRET=your_super_secret_jwt_key_change_in_production
JWT_EXPIRES_IN=24h

# Frontend Configuration
FRONTEND_URL=http://localhost:3000

# Email Configuration (optional)
SMTP_HOST=smtp.gmail.com
SMTP_PORT=587
SMTP_USER=<EMAIL>
SMTP_PASS=your_app_password
FROM_EMAIL=<EMAIL>

# External API Keys
IPAPI_KEY=your_ipapi_key
GEOIP_KEY=your_geoip_key

# Proxy Configuration
MAX_CONNECTIONS_PER_USER=10
RATE_LIMIT_WINDOW_MS=900000
RATE_LIMIT_MAX_REQUESTS=100

# Security Configuration
BCRYPT_ROUNDS=12
SESSION_SECRET=your_session_secret_change_in_production

# Monitoring Configuration
ENABLE_METRICS=true
METRICS_PORT=9090

# Logging Configuration
LOG_LEVEL=info
LOG_FILE=logs/app.log
ERROR_LOG_FILE=logs/error.log

# Development Configuration
ENABLE_CORS=true
ENABLE_SWAGGER=true
DEBUG_MODE=false
