const { initializeDatabase } = require('../models');
const logger = require('../utils/logger');

// Database connection function
const connectDatabase = async () => {
  try {
    await initializeDatabase();
    logger.info('✅ Database connected and initialized successfully');
  } catch (error) {
    logger.error('❌ Database connection failed:', error);
    throw error;
  }
};

module.exports = {
  connectDatabase
};
