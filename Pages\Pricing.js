import React, { useState } from 'react';
import { Card, Card<PERSON>ontent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { CheckCircle, Star, Zap, Shield, Globe, Crown } from 'lucide-react';
import { motion } from 'framer-motion';
import { Link } from 'react-router-dom';
import { createPageUrl } from '@/utils';

export default function Pricing() {
  const [billingCycle, setBillingCycle] = useState('monthly');

  const plans = [
    {
      name: 'FREE TRIAL',
      price: { monthly: 0, annual: 0 },
      duration: '7 DAYS',
      popular: false,
      color: '#666666',
      features: [
        '5 Countries Available',
        'Standard Speed Servers',
        'Basic Support',
        'Single Device',
        'Standard Encryption'
      ],
      limitations: [
        'Limited to 1GB/day',
        'No Kill Switch',
        'Basic Locations Only'
      ]
    },
    {
      name: 'PRO',
      price: { monthly: 9.99, annual: 79.99 },
      duration: '/MONTH',
      popular: true,
      color: '#FF0066',
      features: [
        '85+ Countries Worldwide',
        'Ultra-Fast Premium Servers',
        'Priority 24/7 Support',
        '10 Simultaneous Devices',
        'Military-Grade AES-256',
        'Kill Switch Protection',
        'DNS Leak Protection',
        'No Bandwidth Limits',
        'P2P/Torrent Support',
        'Streaming Optimized'
      ],
      limitations: []
    },
    {
      name: 'ENTERPRISE',
      price: { monthly: 29.99, annual: 299.99 },
      duration: '/MONTH',
      popular: false,
      color: '#0066FF',
      features: [
        'Everything in Pro',
        'Dedicated Server Access',
        'Custom IP Addresses',
        'API Access & Integration',
        'Team Management Portal',
        'Advanced Analytics',
        'SLA Guarantee (99.9%)',
        'Phone Support',
        'Custom Configurations',
        'Dedicated Account Manager'
      ],
      limitations: []
    }
  ];

  const getPrice = (plan) => {
    if (plan.price.monthly === 0) return 'FREE';
    if (billingCycle === 'annual') {
      return `$${(plan.price.annual / 12).toFixed(2)}`;
    }
    return `$${plan.price.monthly}`;
  };

  const getSavings = (plan) => {
    if (plan.price.monthly === 0) return null;
    const monthlyTotal = plan.price.monthly * 12;
    const savings = ((monthlyTotal - plan.price.annual) / monthlyTotal * 100).toFixed(0);
    return billingCycle === 'annual' ? `SAVE ${savings}%` : null;
  };

  return (
    <div className="min-h-screen bg-white p-6 font-mono">
      <div className="max-w-7xl mx-auto">
        {/* Header */}
        <div className="text-center mb-12">
          <h1 className="text-6xl font-black text-black mb-4 transform rotate-2">
            BRUTAL PRICING
          </h1>
          <p className="text-2xl text-black font-bold mb-8 transform -rotate-1">
            CHOOSE YOUR LEVEL OF BRUTALITY
          </p>
          
          {/* Billing Toggle */}
          <div className="inline-flex border-4 border-black bg-white shadow-[4px_4px_0px_0px_#000000]">
            <Button
              onClick={() => setBillingCycle('monthly')}
              className={`font-black border-0 rounded-none ${
                billingCycle === 'monthly'
                  ? 'bg-[#FF0066] text-white'
                  : 'bg-white text-black hover:bg-gray-100'
              }`}
            >
              MONTHLY
            </Button>
            <Button
              onClick={() => setBillingCycle('annual')}
              className={`font-black border-0 rounded-none ${
                billingCycle === 'annual'
                  ? 'bg-[#00FF66] text-black'
                  : 'bg-white text-black hover:bg-gray-100'
              }`}
            >
              ANNUAL
              <Badge className="ml-2 bg-[#FFFF00] text-black border-2 border-black">
                SAVE 33%
              </Badge>
            </Button>
          </div>
        </div>

        {/* Pricing Cards */}
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-8 mb-12">
          {plans.map((plan, index) => (
            <motion.div
              key={plan.name}
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: index * 0.2 }}
              className={`relative ${plan.popular ? 'lg:scale-110 z-10' : ''}`}
            >
              {plan.popular && (
                <Badge className="absolute -top-6 left-1/2 transform -translate-x-1/2 bg-[#FF0066] text-white border-4 border-black font-black text-lg px-6 py-2 z-20">
                  <Star className="w-5 h-5 mr-2" />
                  MOST POPULAR
                </Badge>
              )}
              
              <Card 
                className={`border-4 border-black bg-white transform hover:rotate-1 transition-all ${
                  plan.popular 
                    ? 'shadow-[12px_12px_0px_0px_#FF0066] rotate-1' 
                    : 'shadow-[8px_8px_0px_0px_#000000]'
                }`}
                style={{ 
                  borderColor: plan.color,
                  boxShadow: plan.popular 
                    ? `12px 12px 0px 0px ${plan.color}` 
                    : `8px 8px 0px 0px ${plan.color}`
                }}
              >
                <CardHeader 
                  className="border-b-4 border-black text-white p-6"
                  style={{ backgroundColor: plan.color }}
                >
                  <CardTitle className="text-center">
                    <h3 className="text-3xl font-black mb-2">{plan.name}</h3>
                    <div className="mb-4">
                      <span className="text-5xl font-black">{getPrice(plan)}</span>
                      {plan.price.monthly > 0 && (
                        <span className="text-xl font-bold">{plan.duration}</span>
                      )}
                    </div>
                    {getSavings(plan) && (
                      <Badge className="bg-[#FFFF00] text-black border-2 border-white font-black">
                        {getSavings(plan)}
                      </Badge>
                    )}
                  </CardTitle>
                </CardHeader>
                
                <CardContent className="p-6">
                  {/* Features */}
                  <div className="mb-6">
                    <h4 className="font-black text-lg mb-4 text-[#00FF66]">✓ INCLUDED:</h4>
                    <ul className="space-y-2">
                      {plan.features.map((feature, idx) => (
                        <li key={idx} className="flex items-center gap-2 font-bold">
                          <CheckCircle className="w-5 h-5 text-[#00FF66] flex-shrink-0" />
                          <span>{feature}</span>
                        </li>
                      ))}
                    </ul>
                  </div>

                  {/* Limitations */}
                  {plan.limitations.length > 0 && (
                    <div className="mb-6">
                      <h4 className="font-black text-lg mb-4 text-[#FF0066]">✗ LIMITATIONS:</h4>
                      <ul className="space-y-2">
                        {plan.limitations.map((limitation, idx) => (
                          <li key={idx} className="flex items-center gap-2 font-bold text-gray-600">
                            <span className="w-5 h-5 flex-shrink-0 text-[#FF0066]">✗</span>
                            <span>{limitation}</span>
                          </li>
                        ))}
                      </ul>
                    </div>
                  )}

                  {/* CTA Button */}
                  <Link to={createPageUrl('Dashboard')}>
                    <Button
                      className={`w-full text-xl font-black py-6 border-4 border-black shadow-[6px_6px_0px_0px_#000000] transform hover:rotate-1 transition-all ${
                        plan.name === 'FREE TRIAL'
                          ? 'bg-[#00FF66] text-black hover:bg-[#00FF66]/90'
                          : plan.name === 'PRO'
                          ? 'bg-[#FF0066] text-white hover:bg-[#FF0066]/90'
                          : 'bg-[#0066FF] text-white hover:bg-[#0066FF]/90'
                      }`}
                    >
                      {plan.name === 'FREE TRIAL' ? (
                        <>
                          <Zap className="w-6 h-6 mr-2" />
                          START FREE TRIAL
                        </>
                      ) : plan.name === 'PRO' ? (
                        <>
                          <Shield className="w-6 h-6 mr-2" />
                          GET PRO NOW
                        </>
                      ) : (
                        <>
                          <Crown className="w-6 h-6 mr-2" />
                          CONTACT SALES
                        </>
                      )}
                    </Button>
                  </Link>

                  {plan.name !== 'FREE TRIAL' && (
                    <p className="text-center text-sm font-bold text-gray-500 mt-4">
                      30-day money-back guarantee
                    </p>
                  )}
                </CardContent>
              </Card>
            </motion.div>
          ))}
        </div>

        {/* Feature Comparison */}
        <Card className="border-4 border-black shadow-[8px_8px_0px_0px_#00FF66] bg-white transform -rotate-1">
          <CardHeader className="border-b-4 border-black bg-[#00FF66] text-black">
            <CardTitle className="text-3xl font-black text-center">
              BRUTAL COMPARISON
            </CardTitle>
          </CardHeader>
          <CardContent className="p-0">
            <div className="overflow-x-auto">
              <table className="w-full">
                <thead>
                  <tr className="border-b-4 border-black">
                    <th className="text-left p-4 font-black text-lg">FEATURES</th>
                    <th className="text-center p-4 font-black text-lg">FREE</th>
                    <th className="text-center p-4 font-black text-lg bg-[#FF0066] text-white">PRO</th>
                    <th className="text-center p-4 font-black text-lg">ENTERPRISE</th>
                  </tr>
                </thead>
                <tbody>
                  {[
                    { feature: 'Countries Available', free: '5', pro: '85+', enterprise: '85+' },
                    { feature: 'Simultaneous Devices', free: '1', pro: '10', enterprise: 'Unlimited' },
                    { feature: 'Bandwidth', free: '1GB/day', pro: 'Unlimited', enterprise: 'Unlimited' },
                    { feature: 'Kill Switch', free: '✗', pro: '✓', enterprise: '✓' },
                    { feature: 'DNS Leak Protection', free: '✗', pro: '✓', enterprise: '✓' },
                    { feature: 'P2P/Torrenting', free: '✗', pro: '✓', enterprise: '✓' },
                    { feature: 'Streaming Support', free: '✗', pro: '✓', enterprise: '✓' },
                    { feature: 'Priority Support', free: '✗', pro: '✓', enterprise: '✓' },
                    { feature: 'API Access', free: '✗', pro: '✗', enterprise: '✓' },
                    { feature: 'Dedicated Servers', free: '✗', pro: '✗', enterprise: '✓' }
                  ].map((row, index) => (
                    <tr key={index} className={`border-b-2 border-gray-200 ${index % 2 === 0 ? 'bg-gray-50' : ''}`}>
                      <td className="p-4 font-bold">{row.feature}</td>
                      <td className="p-4 text-center font-bold">{row.free}</td>
                      <td className="p-4 text-center font-bold bg-[#FF0066]/10">{row.pro}</td>
                      <td className="p-4 text-center font-bold">{row.enterprise}</td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  );
}