@echo off
setlocal enabledelayedexpansion

REM ProxyForge Setup Script for Windows
REM This script sets up the ProxyForge development environment on Windows

echo.
echo ========================================
echo   ProxyForge Setup Script for Windows
echo ========================================
echo.

REM Check if Node.js is installed
echo [INFO] Checking Node.js installation...
node --version >nul 2>&1
if errorlevel 1 (
    echo [ERROR] Node.js is not installed. Please install Node.js 18 or higher.
    echo Download from: https://nodejs.org/
    pause
    exit /b 1
)

for /f "tokens=1 delims=v" %%i in ('node --version') do set NODE_VERSION=%%i
echo [SUCCESS] Node.js version %NODE_VERSION% found

REM Check if npm is installed
echo [INFO] Checking npm installation...
npm --version >nul 2>&1
if errorlevel 1 (
    echo [ERROR] npm is not installed. Please install npm.
    pause
    exit /b 1
)

for /f %%i in ('npm --version') do set NPM_VERSION=%%i
echo [SUCCESS] npm version %NPM_VERSION% found

REM Install dependencies
echo.
echo [INFO] Installing project dependencies...
echo [INFO] This may take a few minutes...

echo [INFO] Installing root dependencies...
call npm install
if errorlevel 1 (
    echo [ERROR] Failed to install root dependencies
    pause
    exit /b 1
)

echo [INFO] Installing backend dependencies...
cd backend
call npm install
if errorlevel 1 (
    echo [ERROR] Failed to install backend dependencies
    cd ..
    pause
    exit /b 1
)
cd ..

echo [INFO] Installing frontend dependencies...
cd frontend
call npm install
if errorlevel 1 (
    echo [ERROR] Failed to install frontend dependencies
    cd ..
    pause
    exit /b 1
)
cd ..

echo [INFO] Installing desktop dependencies...
cd desktop
call npm install
if errorlevel 1 (
    echo [ERROR] Failed to install desktop dependencies
    cd ..
    pause
    exit /b 1
)
cd ..

echo [SUCCESS] All dependencies installed successfully

REM Setup environment files
echo.
echo [INFO] Setting up environment configuration...

if not exist "backend\.env" (
    copy "backend\.env.example" "backend\.env" >nul
    echo [SUCCESS] Created backend\.env from template
    echo [WARNING] Please update backend\.env with your configuration
) else (
    echo [INFO] backend\.env already exists
)

REM Create directories
echo [INFO] Creating necessary directories...
if not exist "backend\database" mkdir "backend\database"
if not exist "backend\logs" mkdir "backend\logs"
if not exist "desktop\build" mkdir "desktop\build"

echo [SUCCESS] Directories created

REM Create placeholder files
echo [INFO] Creating placeholder files...
if not exist "desktop\build\icon.png" (
    echo. > "desktop\build\icon.png"
    echo. > "desktop\build\icon.ico"
    echo. > "desktop\build\icon.icns"
    echo. > "desktop\build\tray.png"
    echo. > "desktop\build\tray.ico"
    echo [WARNING] Placeholder icon files created. Replace with actual icons for production.
)

REM Check Docker installation
echo.
echo [INFO] Checking Docker installation...
docker --version >nul 2>&1
if errorlevel 1 (
    echo [WARNING] Docker not found. Docker-based deployment will not be available.
    echo Download from: https://www.docker.com/products/docker-desktop
) else (
    for /f "tokens=3" %%i in ('docker --version') do set DOCKER_VERSION=%%i
    echo [SUCCESS] Docker version !DOCKER_VERSION! found
    
    docker-compose --version >nul 2>&1
    if errorlevel 1 (
        echo [WARNING] Docker Compose not found. Some features may not work.
    ) else (
        for /f "tokens=3" %%i in ('docker-compose --version') do set COMPOSE_VERSION=%%i
        echo [SUCCESS] Docker Compose version !COMPOSE_VERSION! found
    )
)

REM Test frontend build
echo.
echo [INFO] Testing frontend build...
cd frontend
call npm run build >nul 2>&1
if errorlevel 1 (
    echo [WARNING] Frontend build test failed
) else (
    echo [SUCCESS] Frontend builds successfully
)
cd ..

REM Final instructions
echo.
echo ========================================
echo           Setup Complete!
echo ========================================
echo.
echo [SUCCESS] ProxyForge has been set up successfully!
echo.
echo Next steps:
echo 1. Update backend\.env with your configuration
echo 2. Start the development servers:
echo    npm run dev
echo.
echo 3. Or start individual services:
echo    Backend:  cd backend ^&^& npm run dev
echo    Frontend: cd frontend ^&^& npm start
echo    Desktop:  cd desktop ^&^& npm run dev
echo.
echo 4. For Docker deployment:
echo    docker-compose up -d
echo.
echo 5. Run tests:
echo    node scripts\test-app.js
echo.
echo For more information, see README.md and DEPLOYMENT.md
echo.
echo Happy coding! 🎉
echo.

pause
