[build]
  publish = "build"
  command = "npm run build"

[build.environment]
  NODE_VERSION = "18"
  NPM_VERSION = "8"

# Redirect all requests to index.html for client-side routing
[[redirects]]
  from = "/*"
  to = "/index.html"
  status = 200

# API proxy for development
[[redirects]]
  from = "/api/*"
  to = "/.netlify/functions/:splat"
  status = 200

# Security headers
[[headers]]
  for = "/*"
  [headers.values]
    X-Frame-Options = "DENY"
    X-XSS-Protection = "1; mode=block"
    X-Content-Type-Options = "nosniff"
    Referrer-Policy = "strict-origin-when-cross-origin"
    Content-Security-Policy = "default-src 'self'; script-src 'self' 'unsafe-inline' 'unsafe-eval'; style-src 'self' 'unsafe-inline' https://fonts.googleapis.com; font-src 'self' https://fonts.gstatic.com; img-src 'self' data: https:; connect-src 'self' ws: wss: https:;"

# Cache static assets
[[headers]]
  for = "/static/*"
  [headers.values]
    Cache-Control = "public, max-age=********, immutable"

# Cache service worker
[[headers]]
  for = "/sw.js"
  [headers.values]
    Cache-Control = "no-cache"

# Functions configuration
[functions]
  directory = "../netlify/functions"
  node_bundler = "esbuild"

# Environment variables for functions
[context.production.environment]
  NODE_ENV = "production"
  REACT_APP_API_URL = "/.netlify/functions"

[context.deploy-preview.environment]
  NODE_ENV = "development"
  REACT_APP_API_URL = "/.netlify/functions"

[context.branch-deploy.environment]
  NODE_ENV = "development"
  REACT_APP_API_URL = "/.netlify/functions"
