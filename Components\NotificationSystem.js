import React, { useState, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { X, CheckCircle, AlertCircle, Info, AlertTriangle } from 'lucide-react';

let notificationId = 0;

export const useNotifications = () => {
  const [notifications, setNotifications] = useState([]);

  const addNotification = (notification) => {
    const id = ++notificationId;
    const newNotification = { id, ...notification };
    
    setNotifications(prev => [...prev, newNotification]);
    
    if (notification.autoClose !== false) {
      setTimeout(() => {
        removeNotification(id);
      }, notification.duration || 5000);
    }
    
    return id;
  };

  const removeNotification = (id) => {
    setNotifications(prev => prev.filter(n => n.id !== id));
  };

  const success = (message, options = {}) => {
    return addNotification({ type: 'success', message, ...options });
  };

  const error = (message, options = {}) => {
    return addNotification({ type: 'error', message, ...options });
  };

  const warning = (message, options = {}) => {
    return addNotification({ type: 'warning', message, ...options });
  };

  const info = (message, options = {}) => {
    return addNotification({ type: 'info', message, ...options });
  };

  return {
    notifications,
    addNotification,
    removeNotification,
    success,
    error,
    warning,
    info
  };
};

export default function NotificationSystem() {
  const { notifications, removeNotification } = useNotifications();

  const getNotificationStyles = (type) => {
    switch (type) {
      case 'success':
        return {
          bg: 'bg-[#00FF66]',
          text: 'text-black',
          icon: CheckCircle
        };
      case 'error':
        return {
          bg: 'bg-[#FF0066]',
          text: 'text-white',
          icon: AlertCircle
        };
      case 'warning':
        return {
          bg: 'bg-[#FFFF00]',
          text: 'text-black',
          icon: AlertTriangle
        };
      case 'info':
        return {
          bg: 'bg-[#0066FF]',
          text: 'text-white',
          icon: Info
        };
      default:
        return {
          bg: 'bg-black',
          text: 'text-white',
          icon: Info
        };
    }
  };

  return (
    <div className="fixed top-4 right-4 z-50 space-y-4 font-mono">
      <AnimatePresence>
        {notifications.map((notification) => {
          const styles = getNotificationStyles(notification.type);
          const Icon = styles.icon;
          
          return (
            <motion.div
              key={notification.id}
              initial={{ opacity: 0, x: 300, rotate: 10 }}
              animate={{ opacity: 1, x: 0, rotate: 0 }}
              exit={{ opacity: 0, x: 300, rotate: -10 }}
              transition={{ type: "spring", stiffness: 100, damping: 15 }}
              className={`max-w-sm p-4 border-4 border-black shadow-[6px_6px_0px_0px_#000000] ${styles.bg} ${styles.text} transform hover:rotate-1 transition-transform`}
            >
              <div className="flex items-start gap-3">
                <Icon className="w-6 h-6 flex-shrink-0 mt-0.5" />
                <div className="flex-1">
                  {notification.title && (
                    <h4 className="font-black text-lg mb-1">{notification.title}</h4>
                  )}
                  <p className="font-bold">{notification.message}</p>
                </div>
                <button
                  onClick={() => removeNotification(notification.id)}
                  className="flex-shrink-0 p-1 hover:bg-black/20 rounded border-2 border-current"
                >
                  <X className="w-4 h-4" />
                </button>
              </div>
            </motion.div>
          );
        })}
      </AnimatePresence>
    </div>
  );
}