import React from 'react';
import { Link, useLocation } from 'react-router-dom';
import { createPageUrl } from '@/utils';
import { 
  Globe, 
  MapPin, 
  BarChart3, 
  Settings, 
  Zap,
  Shield,
  Menu,
  X
} from 'lucide-react';
import { Button } from '@/components/ui/button';
import { motion, AnimatePresence } from 'framer-motion';

const navigationItems = [
  {
    title: 'DASHBOARD',
    url: createPageUrl('Dashboard'),
    icon: Globe,
    color: '#0066FF'
  },
  {
    title: 'LOCATIONS',
    url: createPageUrl('LocationSelector'),
    icon: MapPin,
    color: '#FF0066'
  },
  {
    title: 'ANALYTICS',
    url: createPageUrl('Analytics'),
    icon: BarChart3,
    color: '#00FF66'
  },
  {
    title: 'SETTINGS',
    url: createPageUrl('Settings'),
    icon: Settings,
    color: '#FFFF00'
  }
];

export default function Layout({ children, currentPageName }) {
  const location = useLocation();
  const [isMobileMenuOpen, setIsMobileMenuOpen] = React.useState(false);

  const isActivePage = (url) => location.pathname === url;

  return (
    <div className="min-h-screen bg-white font-mono">
      {/* Mobile Menu Button */}
      <div className="lg:hidden fixed top-4 left-4 z-50">
        <Button
          onClick={() => setIsMobileMenuOpen(!isMobileMenuOpen)}
          className="font-black border-4 border-black shadow-[4px_4px_0px_0px_#000000] bg-[#FF0066] text-white hover:bg-[#FF0066]/90 p-3"
        >
          {isMobileMenuOpen ? <X className="w-6 h-6" /> : <Menu className="w-6 h-6" />}
        </Button>
      </div>

      {/* Sidebar */}
      <div className={`fixed left-0 top-0 h-full w-80 bg-black border-r-8 border-black z-40 transform transition-transform duration-300 ${
        isMobileMenuOpen ? 'translate-x-0' : '-translate-x-full lg:translate-x-0'
      }`}>
        {/* Logo */}
        <div className="p-8 border-b-4 border-[#FF0066]">
          <div className="flex items-center gap-4">
            <div className="w-16 h-16 bg-[#FF0066] border-4 border-white flex items-center justify-center transform rotate-12">
              <Zap className="w-8 h-8 text-white" />
            </div>
            <div>
              <h1 className="text-2xl font-black text-white transform -rotate-2">
                GEOSHIFT
              </h1>
              <p className="text-lg font-black text-[#00FF66] transform rotate-1">
                PRO
              </p>
            </div>
          </div>
        </div>

        {/* Navigation */}
        <nav className="p-6">
          <div className="space-y-4">
            {navigationItems.map((item, index) => (
              <motion.div
                key={item.title}
                initial={{ opacity: 0, x: -20 }}
                animate={{ opacity: 1, x: 0 }}
                transition={{ delay: index * 0.1 }}
              >
                <Link
                  to={item.url}
                  onClick={() => setIsMobileMenuOpen(false)}
                  className={`block p-4 border-4 border-white font-black text-lg transform hover:rotate-1 hover:scale-105 transition-all shadow-[4px_4px_0px_0px_#000000] ${
                    isActivePage(item.url)
                      ? 'bg-white text-black rotate-2 scale-105'
                      : 'bg-transparent text-white hover:bg-white hover:text-black'
                  }`}
                  style={{
                    backgroundColor: isActivePage(item.url) ? item.color : 'transparent',
                    color: isActivePage(item.url) ? (item.color === '#FFFF00' ? 'black' : 'white') : 'white'
                  }}
                >
                  <div className="flex items-center gap-4">
                    <item.icon className="w-6 h-6" />
                    <span>{item.title}</span>
                  </div>
                </Link>
              </motion.div>
            ))}
          </div>
        </nav>

        {/* Status Panel */}
        <div className="absolute bottom-0 left-0 right-0 p-6 border-t-4 border-[#00FF66]">
          <div className="bg-[#00FF66] border-4 border-white p-4 text-black font-black transform -rotate-1">
            <div className="flex items-center gap-2 mb-2">
              <Shield className="w-5 h-5" />
              <span>STATUS: SECURE</span>
            </div>
            <div className="text-sm">
              <p>IP: *************</p>
              <p>LOCATION: PROTECTED</p>
            </div>
          </div>
        </div>
      </div>

      {/* Mobile Menu Overlay */}
      <AnimatePresence>
        {isMobileMenuOpen && (
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
            className="lg:hidden fixed inset-0 bg-black/50 z-30"
            onClick={() => setIsMobileMenuOpen(false)}
          />
        )}
      </AnimatePresence>

      {/* Main Content */}
      <div className="lg:ml-80 min-h-screen">
        {/* Top Bar */}
        <div className="bg-black border-b-8 border-[#FF0066] p-4 lg:p-6">
          <div className="flex items-center justify-between">
            <div className="lg:block hidden">
              <h2 className="text-2xl font-black text-white transform rotate-1">
                {currentPageName?.toUpperCase() || 'GEOSHIFT PRO'}
              </h2>
              <p className="text-[#00FF66] font-bold">
                BRUTAL IP LOCATION SWITCHER
              </p>
            </div>
            <div className="lg:hidden flex-1 text-center">
              <h2 className="text-xl font-black text-white">
                {currentPageName?.toUpperCase() || 'GEOSHIFT PRO'}
              </h2>
            </div>
            <div className="flex items-center gap-4">
              <div className="w-4 h-4 bg-[#00FF66] border-2 border-white rounded-full animate-pulse" />
              <span className="text-white font-black hidden sm:inline">ONLINE</span>
            </div>
          </div>
        </div>

        {/* Page Content */}
        <main className="p-0">
          {children}
        </main>
      </div>

      {/* Neo-Brutalist Styles */}
      <style jsx global>{`
        @import url('https://fonts.googleapis.com/css2?family=Space+Mono:wght@400;700&display=swap');
        
        * {
          font-family: 'Space Mono', monospace !important;
        }
        
        .transform {
          transform-origin: center;
        }
        
        button:active {
          transform: translate(2px, 2px);
          box-shadow: 2px 2px 0px 0px #000000 !important;
        }
        
        .hover\\:rotate-1:hover {
          transform: rotate(1deg);
        }
        
        .hover\\:rotate-0:hover {
          transform: rotate(0deg);
        }
        
        .rotate-0\\.5 {
          transform: rotate(0.5deg);
        }
        
        .-rotate-0\\.5 {
          transform: rotate(-0.5deg);
        }
        
        .rotate-12 {
          transform: rotate(12deg);
        }
        
        /* Custom scrollbar */
        ::-webkit-scrollbar {
          width: 8px;
        }
        
        ::-webkit-scrollbar-track {
          background: #fff;
          border: 2px solid #000;
        }
        
        ::-webkit-scrollbar-thumb {
          background: #FF0066;
          border: 2px solid #000;
        }
        
        ::-webkit-scrollbar-thumb:hover {
          background: #00FF66;
        }
      `}</style>
    </div>
  );
}