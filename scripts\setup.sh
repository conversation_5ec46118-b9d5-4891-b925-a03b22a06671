#!/bin/bash

# ProxyForge Setup Script
# This script sets up the ProxyForge development environment

set -e  # Exit on any error

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
PURPLE='\033[0;35m'
CYAN='\033[0;36m'
NC='\033[0m' # No Color

# Logging functions
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

log_header() {
    echo -e "${PURPLE}$1${NC}"
}

# Check if command exists
command_exists() {
    command -v "$1" >/dev/null 2>&1
}

# Check Node.js version
check_node_version() {
    if command_exists node; then
        NODE_VERSION=$(node --version | cut -d'v' -f2)
        MAJOR_VERSION=$(echo $NODE_VERSION | cut -d'.' -f1)
        
        if [ "$MAJOR_VERSION" -ge 18 ]; then
            log_success "Node.js version $NODE_VERSION is compatible"
            return 0
        else
            log_error "Node.js version $NODE_VERSION is not compatible. Please install Node.js 18 or higher."
            return 1
        fi
    else
        log_error "Node.js is not installed. Please install Node.js 18 or higher."
        return 1
    fi
}

# Check npm version
check_npm_version() {
    if command_exists npm; then
        NPM_VERSION=$(npm --version)
        log_success "npm version $NPM_VERSION found"
        return 0
    else
        log_error "npm is not installed. Please install npm."
        return 1
    fi
}

# Install dependencies
install_dependencies() {
    log_info "Installing project dependencies..."
    
    # Install root dependencies
    log_info "Installing root dependencies..."
    npm install
    
    # Install backend dependencies
    log_info "Installing backend dependencies..."
    cd backend && npm install && cd ..
    
    # Install frontend dependencies
    log_info "Installing frontend dependencies..."
    cd frontend && npm install && cd ..
    
    # Install desktop dependencies
    log_info "Installing desktop dependencies..."
    cd desktop && npm install && cd ..
    
    log_success "All dependencies installed successfully"
}

# Setup environment files
setup_environment() {
    log_info "Setting up environment configuration..."
    
    if [ ! -f "backend/.env" ]; then
        cp backend/.env.example backend/.env
        log_success "Created backend/.env from template"
        log_warning "Please update backend/.env with your configuration"
    else
        log_info "backend/.env already exists"
    fi
    
    # Generate JWT secret if not set
    if grep -q "your_super_secret_jwt_key_change_in_production" backend/.env; then
        JWT_SECRET=$(openssl rand -base64 32 2>/dev/null || head -c 32 /dev/urandom | base64)
        if [ -n "$JWT_SECRET" ]; then
            sed -i.bak "s/your_super_secret_jwt_key_change_in_production/$JWT_SECRET/" backend/.env
            log_success "Generated JWT secret"
        else
            log_warning "Could not generate JWT secret automatically. Please update it manually."
        fi
    fi
}

# Setup database
setup_database() {
    log_info "Setting up database..."
    
    # Create database directory
    mkdir -p backend/database
    mkdir -p backend/logs
    
    log_success "Database directories created"
}

# Check Docker installation
check_docker() {
    if command_exists docker; then
        DOCKER_VERSION=$(docker --version | cut -d' ' -f3 | cut -d',' -f1)
        log_success "Docker version $DOCKER_VERSION found"
        
        if command_exists docker-compose; then
            COMPOSE_VERSION=$(docker-compose --version | cut -d' ' -f3 | cut -d',' -f1)
            log_success "Docker Compose version $COMPOSE_VERSION found"
        else
            log_warning "Docker Compose not found. Some features may not work."
        fi
    else
        log_warning "Docker not found. Docker-based deployment will not be available."
    fi
}

# Setup Git hooks
setup_git_hooks() {
    if [ -d ".git" ]; then
        log_info "Setting up Git hooks..."
        
        # Install husky if not already installed
        if [ -f "package.json" ] && grep -q "husky" package.json; then
            npx husky install
            log_success "Git hooks configured"
        fi
    else
        log_warning "Not a Git repository. Git hooks will not be configured."
    fi
}

# Create desktop icons and build directories
setup_desktop() {
    log_info "Setting up desktop application..."
    
    mkdir -p desktop/build
    
    # Create placeholder icon files
    if [ ! -f "desktop/build/icon.png" ]; then
        log_info "Creating placeholder desktop icons..."
        # In a real setup, you would copy actual icon files here
        touch desktop/build/icon.png
        touch desktop/build/icon.ico
        touch desktop/build/icon.icns
        touch desktop/build/tray.png
        touch desktop/build/tray.ico
        log_warning "Placeholder icon files created. Replace with actual icons for production."
    fi
}

# Test installation
test_installation() {
    log_info "Testing installation..."
    
    # Test if we can start the backend
    log_info "Testing backend startup..."
    cd backend
    timeout 10s npm start > /dev/null 2>&1 &
    BACKEND_PID=$!
    sleep 5
    
    if kill -0 $BACKEND_PID 2>/dev/null; then
        kill $BACKEND_PID
        log_success "Backend starts successfully"
    else
        log_warning "Backend startup test failed"
    fi
    cd ..
    
    # Test if we can build the frontend
    log_info "Testing frontend build..."
    cd frontend
    if npm run build > /dev/null 2>&1; then
        log_success "Frontend builds successfully"
    else
        log_warning "Frontend build test failed"
    fi
    cd ..
}

# Main setup function
main() {
    log_header "🚀 ProxyForge Setup Script"
    echo "This script will set up your ProxyForge development environment."
    echo ""
    
    # Check prerequisites
    log_header "📋 Checking Prerequisites"
    check_node_version || exit 1
    check_npm_version || exit 1
    check_docker
    
    # Setup project
    log_header "🛠️  Setting Up Project"
    install_dependencies
    setup_environment
    setup_database
    setup_desktop
    setup_git_hooks
    
    # Test installation
    log_header "🧪 Testing Installation"
    test_installation
    
    # Final instructions
    log_header "✅ Setup Complete!"
    echo ""
    log_success "ProxyForge has been set up successfully!"
    echo ""
    echo "Next steps:"
    echo "1. Update backend/.env with your configuration"
    echo "2. Start the development servers:"
    echo "   npm run dev"
    echo ""
    echo "3. Or start individual services:"
    echo "   Backend:  cd backend && npm run dev"
    echo "   Frontend: cd frontend && npm start"
    echo "   Desktop:  cd desktop && npm run dev"
    echo ""
    echo "4. For Docker deployment:"
    echo "   docker-compose up -d"
    echo ""
    echo "5. Run tests:"
    echo "   node scripts/test-app.js"
    echo ""
    log_info "For more information, see README.md and DEPLOYMENT.md"
    echo ""
    log_success "Happy coding! 🎉"
}

# Handle command line arguments
case "${1:-}" in
    --help|-h)
        echo "ProxyForge Setup Script"
        echo ""
        echo "Usage: $0 [options]"
        echo ""
        echo "Options:"
        echo "  --help, -h     Show this help message"
        echo "  --skip-deps    Skip dependency installation"
        echo "  --skip-test    Skip installation testing"
        echo ""
        exit 0
        ;;
    --skip-deps)
        SKIP_DEPS=true
        ;;
    --skip-test)
        SKIP_TEST=true
        ;;
esac

# Run main setup
main
