import React from 'react';
import { BrowserRouter as Router, Routes, Route, Navigate } from 'react-router-dom';
import { QueryClient, QueryClientProvider } from 'react-query';
import { Toaster } from 'react-hot-toast';
import { AnimatePresence } from 'framer-motion';

// Layout and Components
import Layout from './Layout';
import ErrorBoundary from './components/ErrorBoundary';
import LoadingScreen from './components/LoadingScreen';
import ProtectedRoute from './components/ProtectedRoute';

// Pages
import Home from './pages/Home';
import Dashboard from './pages/Dashboard';
import LocationSelector from './pages/LocationSelector';
import Analytics from './pages/Analytics';
import Settings from './pages/Settings';
import Pricing from './pages/Pricing';
import About from './pages/About';
import Contact from './pages/Contact';
import Login from './pages/Login';
import Register from './pages/Register';

// Services and Hooks
import { AuthProvider, useAuth } from './hooks/useAuth';
import { SocketProvider } from './hooks/useSocket';

// Styles
import './index.css';

// Create React Query client
const queryClient = new QueryClient({
  defaultOptions: {
    queries: {
      retry: 3,
      retryDelay: attemptIndex => Math.min(1000 * 2 ** attemptIndex, 30000),
      staleTime: 5 * 60 * 1000, // 5 minutes
      cacheTime: 10 * 60 * 1000, // 10 minutes
    },
    mutations: {
      retry: 1,
    },
  },
});

// Route configuration
const routes = [
  { path: '/', element: <Home />, public: true },
  { path: '/login', element: <Login />, public: true },
  { path: '/register', element: <Register />, public: true },
  { path: '/pricing', element: <Pricing />, public: true },
  { path: '/about', element: <About />, public: true },
  { path: '/contact', element: <Contact />, public: true },
  { path: '/dashboard', element: <Dashboard />, protected: true },
  { path: '/locations', element: <LocationSelector />, protected: true },
  { path: '/analytics', element: <Analytics />, protected: true },
  { path: '/settings', element: <Settings />, protected: true },
];

// Main App Component
function App() {
  return (
    <ErrorBoundary>
      <QueryClientProvider client={queryClient}>
        <AuthProvider>
          <SocketProvider>
            <Router>
              <div className="App">
                <AnimatePresence mode="wait">
                  <Routes>
                    {routes.map(({ path, element, public: isPublic, protected: isProtected }) => (
                      <Route
                        key={path}
                        path={path}
                        element={
                          isProtected ? (
                            <ProtectedRoute>
                              <Layout currentPageName={getPageName(path)}>
                                {element}
                              </Layout>
                            </ProtectedRoute>
                          ) : isPublic ? (
                            path === '/' ? element : (
                              <Layout currentPageName={getPageName(path)}>
                                {element}
                              </Layout>
                            )
                          ) : (
                            <Layout currentPageName={getPageName(path)}>
                              {element}
                            </Layout>
                          )
                        }
                      />
                    ))}
                    
                    {/* Redirect unknown routes */}
                    <Route path="*" element={<Navigate to="/" replace />} />
                  </Routes>
                </AnimatePresence>

                {/* Global Toast Notifications */}
                <Toaster
                  position="top-right"
                  toastOptions={{
                    duration: 4000,
                    style: {
                      background: '#000',
                      color: '#fff',
                      border: '4px solid #FF0066',
                      borderRadius: '0',
                      fontFamily: 'Space Mono, monospace',
                      fontWeight: 'bold',
                      fontSize: '14px',
                      boxShadow: '4px 4px 0px 0px #FF0066',
                    },
                    success: {
                      style: {
                        border: '4px solid #00FF66',
                        boxShadow: '4px 4px 0px 0px #00FF66',
                      },
                    },
                    error: {
                      style: {
                        border: '4px solid #FF0066',
                        boxShadow: '4px 4px 0px 0px #FF0066',
                      },
                    },
                  }}
                />

                {/* Loading Screen for Initial App Load */}
                <React.Suspense fallback={<LoadingScreen />}>
                  <div />
                </React.Suspense>
              </div>
            </Router>
          </SocketProvider>
        </AuthProvider>
      </QueryClientProvider>
    </ErrorBoundary>
  );
}

// Helper function to get page name from path
function getPageName(path) {
  const pathMap = {
    '/': 'Home',
    '/dashboard': 'Dashboard',
    '/locations': 'Location Selector',
    '/analytics': 'Analytics',
    '/settings': 'Settings',
    '/pricing': 'Pricing',
    '/about': 'About',
    '/contact': 'Contact',
    '/login': 'Login',
    '/register': 'Register',
  };
  
  return pathMap[path] || 'ProxyForge';
}

// App wrapper with error boundary and loading states
function AppWrapper() {
  return (
    <ErrorBoundary>
      <React.Suspense fallback={<LoadingScreen />}>
        <App />
      </React.Suspense>
    </ErrorBoundary>
  );
}

export default AppWrapper;
