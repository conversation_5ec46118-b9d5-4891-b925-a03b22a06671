{"name": "UserPreferences", "type": "object", "properties": {"auto_connect": {"type": "boolean", "default": true, "description": "Auto-connect on startup"}, "kill_switch": {"type": "boolean", "default": true, "description": "Enable kill switch protection"}, "dns_leak_protection": {"type": "boolean", "default": true, "description": "Prevent DNS leaks"}, "webrtc_blocking": {"type": "boolean", "default": false, "description": "Block WebRTC IP leaks"}, "auto_rotation": {"type": "boolean", "default": false, "description": "Automatically rotate locations"}, "rotation_interval": {"type": "number", "default": 30, "description": "Rotation interval in minutes"}, "preferred_protocol": {"type": "string", "enum": ["openvpn", "wireguard", "ikev2"], "default": "openvpn", "description": "Preferred VPN protocol"}, "encryption_level": {"type": "string", "enum": ["aes128", "aes256", "chacha20"], "default": "aes256", "description": "Encryption strength"}, "custom_dns": {"type": "string", "description": "Custom DNS servers"}, "start_minimized": {"type": "boolean", "default": false, "description": "Start application minimized"}, "notifications": {"type": "boolean", "default": true, "description": "Show notifications"}, "theme": {"type": "string", "enum": ["brutal", "dark", "light"], "default": "brutal", "description": "UI theme"}, "favorite_countries": {"type": "array", "items": {"type": "string"}, "description": "List of favorite countries"}}, "required": []}