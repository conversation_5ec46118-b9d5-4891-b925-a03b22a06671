const http = require('http');
const https = require('https');
const net = require('net');
const url = require('url');
const axios = require('axios');
const logger = require('../utils/logger');
const { ProxyServer, Connection } = require('../models');

class ProxyService {
  constructor() {
    this.proxyServers = new Map();
    this.activeConnections = new Map();
    this.proxyPool = [];
    this.currentProxyIndex = 0;
    this.server = null;
    this.rateLimits = new Map(); // userId -> { requests: number, resetTime: Date }
  }

  async initialize() {
    try {
      await this.loadProxyPool();
      await this.testProxyPool();
      logger.info('ProxyService initialized successfully');
    } catch (error) {
      logger.error('Failed to initialize ProxyService:', error);
      throw error;
    }
  }

  async loadProxyPool() {
    try {
      // Load from database
      const dbProxies = await ProxyServer.findAll({
        where: { isActive: true }
      });

      // Load from free proxy APIs
      const freeProxies = await this.fetchFreeProxies();
      
      this.proxyPool = [
        ...dbProxies.map(p => ({
          id: p.id,
          host: p.host,
          port: p.port,
          type: p.type,
          country: p.country,
          speed: p.speed,
          reliability: p.reliability,
          source: 'database'
        })),
        ...freeProxies
      ];

      logger.info(`Loaded ${this.proxyPool.length} proxies into pool`);
    } catch (error) {
      logger.error('Error loading proxy pool:', error);
      // Fallback to default proxies
      this.proxyPool = this.getDefaultProxies();
    }
  }

  async fetchFreeProxies() {
    const freeProxies = [];
    
    try {
      // ProxyList API (example - replace with actual free APIs)
      const response = await axios.get('https://api.proxyscrape.com/v2/', {
        params: {
          request: 'get',
          protocol: 'http',
          timeout: 10000,
          country: 'all',
          ssl: 'all',
          anonymity: 'all'
        },
        timeout: 5000
      });

      const proxyList = response.data.split('\n').filter(line => line.trim());
      
      for (const proxy of proxyList.slice(0, 50)) { // Limit to 50 proxies
        const [host, port] = proxy.split(':');
        if (host && port) {
          freeProxies.push({
            host: host.trim(),
            port: parseInt(port.trim()),
            type: 'http',
            country: 'unknown',
            speed: 0,
            reliability: 0.5,
            source: 'free-api'
          });
        }
      }
    } catch (error) {
      logger.warn('Failed to fetch free proxies:', error.message);
    }

    return freeProxies;
  }

  getDefaultProxies() {
    return [
      {
        id: 'default-1',
        host: '*******',
        port: 80,
        type: 'http',
        country: 'US',
        speed: 100,
        reliability: 0.9,
        source: 'default'
      },
      {
        id: 'default-2',
        host: '*******',
        port: 80,
        type: 'http',
        country: 'US',
        speed: 95,
        reliability: 0.95,
        source: 'default'
      }
    ];
  }

  async testProxyPool() {
    const workingProxies = [];
    
    for (const proxy of this.proxyPool) {
      try {
        const isWorking = await this.testProxy(proxy);
        if (isWorking) {
          workingProxies.push(proxy);
        }
      } catch (error) {
        logger.debug(`Proxy ${proxy.host}:${proxy.port} failed test:`, error.message);
      }
    }

    this.proxyPool = workingProxies;
    logger.info(`${workingProxies.length} proxies passed testing`);
  }

  async testProxy(proxy, timeout = 5000) {
    return new Promise((resolve) => {
      const testUrl = 'http://httpbin.org/ip';
      const options = {
        hostname: proxy.host,
        port: proxy.port,
        path: testUrl,
        method: 'GET',
        timeout: timeout,
        headers: {
          'User-Agent': 'ProxyForge-Test/1.0'
        }
      };

      const req = http.request(options, (res) => {
        let data = '';
        res.on('data', chunk => data += chunk);
        res.on('end', () => {
          try {
            const result = JSON.parse(data);
            resolve(!!result.origin);
          } catch {
            resolve(false);
          }
        });
      });

      req.on('error', () => resolve(false));
      req.on('timeout', () => {
        req.destroy();
        resolve(false);
      });

      req.end();
    });
  }

  startProxyServer(port) {
    this.server = http.createServer((req, res) => {
      this.handleHttpRequest(req, res);
    });

    this.server.on('connect', (req, clientSocket, head) => {
      this.handleHttpsRequest(req, clientSocket, head);
    });

    this.server.listen(port, () => {
      logger.info(`🔗 Proxy server listening on port ${port}`);
    });

    return this.server;
  }

  async handleHttpRequest(req, res) {
    try {
      const userId = req.headers['x-user-id'];
      
      if (!userId) {
        res.writeHead(401, { 'Content-Type': 'application/json' });
        res.end(JSON.stringify({ error: 'Unauthorized' }));
        return;
      }

      // Check rate limits
      if (!this.checkRateLimit(userId)) {
        res.writeHead(429, { 'Content-Type': 'application/json' });
        res.end(JSON.stringify({ error: 'Rate limit exceeded' }));
        return;
      }

      const proxy = this.getNextProxy();
      if (!proxy) {
        res.writeHead(503, { 'Content-Type': 'application/json' });
        res.end(JSON.stringify({ error: 'No proxy servers available' }));
        return;
      }

      await this.forwardHttpRequest(req, res, proxy, userId);
      
    } catch (error) {
      logger.error('Error handling HTTP request:', error);
      res.writeHead(500, { 'Content-Type': 'application/json' });
      res.end(JSON.stringify({ error: 'Internal server error' }));
    }
  }

  async handleHttpsRequest(req, clientSocket, head) {
    try {
      const userId = req.headers['x-user-id'];
      
      if (!userId) {
        clientSocket.write('HTTP/1.1 401 Unauthorized\r\n\r\n');
        clientSocket.end();
        return;
      }

      if (!this.checkRateLimit(userId)) {
        clientSocket.write('HTTP/1.1 429 Too Many Requests\r\n\r\n');
        clientSocket.end();
        return;
      }

      const proxy = this.getNextProxy();
      if (!proxy) {
        clientSocket.write('HTTP/1.1 503 Service Unavailable\r\n\r\n');
        clientSocket.end();
        return;
      }

      await this.forwardHttpsRequest(req, clientSocket, head, proxy, userId);
      
    } catch (error) {
      logger.error('Error handling HTTPS request:', error);
      clientSocket.write('HTTP/1.1 500 Internal Server Error\r\n\r\n');
      clientSocket.end();
    }
  }

  async forwardHttpRequest(req, res, proxy, userId) {
    const options = {
      hostname: proxy.host,
      port: proxy.port,
      path: req.url,
      method: req.method,
      headers: {
        ...req.headers,
        'X-Forwarded-For': req.connection.remoteAddress,
        'X-ProxyForge-User': userId
      }
    };

    const proxyReq = http.request(options, (proxyRes) => {
      res.writeHead(proxyRes.statusCode, proxyRes.headers);
      proxyRes.pipe(res);
      
      // Log connection
      this.logConnection(userId, proxy, req.url, proxyRes.statusCode);
    });

    proxyReq.on('error', (error) => {
      logger.error('Proxy request error:', error);
      res.writeHead(502, { 'Content-Type': 'application/json' });
      res.end(JSON.stringify({ error: 'Bad Gateway' }));
    });

    req.pipe(proxyReq);
  }

  async forwardHttpsRequest(req, clientSocket, head, proxy, userId) {
    const serverSocket = net.connect(proxy.port, proxy.host, () => {
      clientSocket.write('HTTP/1.1 200 Connection Established\r\n\r\n');
      serverSocket.write(head);
      serverSocket.pipe(clientSocket);
      clientSocket.pipe(serverSocket);
      
      // Log connection
      this.logConnection(userId, proxy, req.url, 200);
    });

    serverSocket.on('error', (error) => {
      logger.error('Server socket error:', error);
      clientSocket.write('HTTP/1.1 502 Bad Gateway\r\n\r\n');
      clientSocket.end();
    });
  }

  getNextProxy() {
    if (this.proxyPool.length === 0) {
      return null;
    }

    const proxy = this.proxyPool[this.currentProxyIndex];
    this.currentProxyIndex = (this.currentProxyIndex + 1) % this.proxyPool.length;
    
    return proxy;
  }

  checkRateLimit(userId) {
    const now = new Date();
    const userLimit = this.rateLimits.get(userId);

    if (!userLimit || now > userLimit.resetTime) {
      // Reset or create new limit
      this.rateLimits.set(userId, {
        requests: 1,
        resetTime: new Date(now.getTime() + 60 * 1000) // 1 minute window
      });
      return true;
    }

    if (userLimit.requests >= 100) { // 100 requests per minute
      return false;
    }

    userLimit.requests++;
    return true;
  }

  async logConnection(userId, proxy, url, statusCode) {
    try {
      await Connection.create({
        userId,
        proxyHost: proxy.host,
        proxyPort: proxy.port,
        targetUrl: url,
        statusCode,
        timestamp: new Date()
      });
    } catch (error) {
      logger.error('Error logging connection:', error);
    }
  }

  async getConnectionStats(userId) {
    try {
      const stats = await Connection.findAll({
        where: { userId },
        attributes: [
          [Connection.sequelize.fn('COUNT', '*'), 'totalConnections'],
          [Connection.sequelize.fn('AVG', Connection.sequelize.col('responseTime')), 'avgResponseTime'],
          [Connection.sequelize.fn('COUNT', Connection.sequelize.literal('CASE WHEN statusCode >= 200 AND statusCode < 300 THEN 1 END')), 'successfulConnections']
        ],
        raw: true
      });

      return {
        totalConnections: stats[0].totalConnections || 0,
        avgResponseTime: Math.round(stats[0].avgResponseTime || 0),
        successRate: stats[0].totalConnections > 0 
          ? Math.round((stats[0].successfulConnections / stats[0].totalConnections) * 100)
          : 0,
        activeProxies: this.proxyPool.length
      };
    } catch (error) {
      logger.error('Error getting connection stats:', error);
      return {
        totalConnections: 0,
        avgResponseTime: 0,
        successRate: 0,
        activeProxies: this.proxyPool.length
      };
    }
  }

  async getProxyList(country = null) {
    let proxies = this.proxyPool;
    
    if (country) {
      proxies = proxies.filter(p => p.country.toLowerCase() === country.toLowerCase());
    }

    return proxies.map(proxy => ({
      id: proxy.id,
      country: proxy.country,
      host: proxy.host,
      port: proxy.port,
      type: proxy.type,
      speed: proxy.speed,
      reliability: proxy.reliability
    }));
  }

  stop() {
    if (this.server) {
      this.server.close();
      logger.info('Proxy server stopped');
    }
  }
}

module.exports = ProxyService;
