# 🚀 ProxyForge Deployment Guide

This guide covers deployment options for ProxyForge across different platforms and environments.

## 📋 Prerequisites

- Node.js 18+ and npm 8+
- Git
- <PERSON>er and Docker Compose (for containerized deployment)
- PostgreSQL (for production)
- Redis (optional, for rate limiting)

## 🛠️ Initial Setup

### 1. Clone and Install Dependencies

```bash
# Clone the repository
git clone https://github.com/HectorTa1989/proxyforge.git
cd proxyforge

# Install all dependencies
npm run install:all

# Set up environment variables
npm run setup:env
```

### 2. Configure Environment Variables

Edit `backend/.env` with your configuration:

```bash
# Database (use PostgreSQL for production)
DATABASE_URL=postgresql://username:password@localhost:5432/proxyforge

# JWT Secret (generate a strong secret)
JWT_SECRET=your_super_secret_jwt_key_change_in_production

# Redis (optional, for distributed rate limiting)
REDIS_URL=redis://localhost:6379

# External APIs (optional)
IPAPI_KEY=your_ipapi_key
```

## 🌐 Web Application Deployment

### Option 1: Netlify (Recommended for Frontend)

1. **Prepare for Deployment**
   ```bash
   cd frontend
   npm run build
   ```

2. **Deploy to Netlify**
   ```bash
   # Install Netlify CLI
   npm install -g netlify-cli

   # Login to Netlify
   netlify login

   # Deploy
   netlify deploy --prod --dir=build
   ```

3. **Configure Environment Variables in Netlify**
   - Go to Site Settings > Environment Variables
   - Add `REACT_APP_API_URL=/.netlify/functions`

### Option 2: Vercel

1. **Install Vercel CLI**
   ```bash
   npm install -g vercel
   ```

2. **Deploy**
   ```bash
   cd frontend
   vercel --prod
   ```

### Option 3: Traditional Web Hosting

1. **Build the Frontend**
   ```bash
   cd frontend
   npm run build
   ```

2. **Upload the `build` folder** to your web hosting provider

3. **Configure Web Server** (Apache/Nginx) for client-side routing

## 🖥️ Desktop Application

### Build for All Platforms

```bash
cd desktop
npm run dist
```

### Platform-Specific Builds

```bash
# Windows
npm run dist:win

# macOS
npm run dist:mac

# Linux
npm run dist:linux
```

### Development Mode

```bash
# Start backend and frontend, then launch Electron
npm run dev
```

## 🐳 Docker Deployment

### Development Environment

```bash
# Start all services
docker-compose up -d

# View logs
docker-compose logs -f
```

### Production Environment

```bash
# Start production services
docker-compose --profile production up -d

# Include monitoring
docker-compose --profile production --profile monitoring up -d
```

### Custom Docker Build

```bash
# Build backend image
cd backend
docker build -t proxyforge-backend .

# Build and run
docker run -p 5000:5000 -p 8080:8080 proxyforge-backend
```

## ☁️ Cloud Deployment

### AWS Deployment

1. **EC2 Instance Setup**
   ```bash
   # Launch Ubuntu 20.04 LTS instance
   # Install Docker and Docker Compose
   sudo apt update
   sudo apt install docker.io docker-compose
   
   # Clone and deploy
   git clone https://github.com/HectorTa1989/proxyforge.git
   cd proxyforge
   docker-compose --profile production up -d
   ```

2. **RDS Database**
   - Create PostgreSQL RDS instance
   - Update `DATABASE_URL` in environment variables

3. **ElastiCache Redis**
   - Create Redis cluster
   - Update `REDIS_URL` in environment variables

### Google Cloud Platform

1. **Cloud Run Deployment**
   ```bash
   # Build and push to Container Registry
   gcloud builds submit --tag gcr.io/PROJECT_ID/proxyforge-backend backend/

   # Deploy to Cloud Run
   gcloud run deploy proxyforge-backend \
     --image gcr.io/PROJECT_ID/proxyforge-backend \
     --platform managed \
     --region us-central1 \
     --allow-unauthenticated
   ```

2. **Cloud SQL**
   - Create PostgreSQL instance
   - Configure connection and update environment variables

### DigitalOcean

1. **Droplet Setup**
   ```bash
   # Create Ubuntu droplet
   # Install Docker
   curl -fsSL https://get.docker.com -o get-docker.sh
   sh get-docker.sh
   
   # Deploy application
   git clone https://github.com/HectorTa1989/proxyforge.git
   cd proxyforge
   docker-compose --profile production up -d
   ```

2. **Managed Database**
   - Create PostgreSQL cluster
   - Update connection string

## 🔧 Backend API Deployment

### Standalone Node.js Deployment

```bash
cd backend

# Install production dependencies
npm ci --only=production

# Build application
npm run build

# Start with PM2
npm install -g pm2
pm2 start dist/server.js --name proxyforge-api

# Set up PM2 startup
pm2 startup
pm2 save
```

### Nginx Reverse Proxy

```nginx
server {
    listen 80;
    server_name your-domain.com;

    location /api {
        proxy_pass http://localhost:5000;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection 'upgrade';
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_cache_bypass $http_upgrade;
    }

    location / {
        root /var/www/proxyforge;
        try_files $uri $uri/ /index.html;
    }
}
```

## 📊 Monitoring and Logging

### Enable Monitoring Stack

```bash
# Start with monitoring
docker-compose --profile monitoring up -d

# Access Grafana at http://localhost:3001
# Default credentials: admin/admin_password_change_in_production
```

### Enable Logging Stack

```bash
# Start with logging
docker-compose --profile logging up -d

# Access Kibana at http://localhost:5601
```

### Health Checks

- Backend API: `http://localhost:5000/health`
- Prometheus: `http://localhost:9090`
- Grafana: `http://localhost:3001`

## 🔒 Security Considerations

### Production Security Checklist

- [ ] Change all default passwords
- [ ] Use strong JWT secrets
- [ ] Enable HTTPS with SSL certificates
- [ ] Configure firewall rules
- [ ] Set up database backups
- [ ] Enable rate limiting
- [ ] Configure CORS properly
- [ ] Use environment variables for secrets
- [ ] Enable security headers
- [ ] Set up monitoring and alerting

### SSL Certificate Setup

```bash
# Using Let's Encrypt with Certbot
sudo apt install certbot python3-certbot-nginx
sudo certbot --nginx -d your-domain.com
```

## 🧪 Testing Deployment

### Health Check Script

```bash
#!/bin/bash
# health-check.sh

echo "Checking ProxyForge deployment..."

# Check backend API
if curl -f http://localhost:5000/health > /dev/null 2>&1; then
    echo "✅ Backend API is healthy"
else
    echo "❌ Backend API is not responding"
fi

# Check frontend
if curl -f http://localhost:3000 > /dev/null 2>&1; then
    echo "✅ Frontend is accessible"
else
    echo "❌ Frontend is not accessible"
fi

# Check proxy server
if curl -f http://localhost:8080 > /dev/null 2>&1; then
    echo "✅ Proxy server is running"
else
    echo "❌ Proxy server is not responding"
fi
```

### Load Testing

```bash
# Install Apache Bench
sudo apt install apache2-utils

# Test API endpoint
ab -n 1000 -c 10 http://localhost:5000/health

# Test proxy connection
ab -n 100 -c 5 http://localhost:5000/api/proxy/servers
```

## 🔄 Updates and Maintenance

### Automated Updates

```bash
# Create update script
#!/bin/bash
# update.sh

git pull origin main
npm run install:all
npm run build
docker-compose down
docker-compose --profile production up -d
```

### Database Migrations

```bash
cd backend
npm run db:migrate
npm run db:backup
```

### Log Rotation

```bash
# Configure logrotate
sudo nano /etc/logrotate.d/proxyforge

/path/to/proxyforge/backend/logs/*.log {
    daily
    missingok
    rotate 30
    compress
    delaycompress
    notifempty
    create 644 proxyforge proxyforge
    postrotate
        systemctl reload proxyforge
    endscript
}
```

## 🆘 Troubleshooting

### Common Issues

1. **Port Already in Use**
   ```bash
   # Find process using port
   lsof -i :5000
   
   # Kill process
   kill -9 PID
   ```

2. **Database Connection Issues**
   ```bash
   # Check PostgreSQL status
   sudo systemctl status postgresql
   
   # Test connection
   psql -h localhost -U username -d proxyforge
   ```

3. **Docker Issues**
   ```bash
   # Clean up Docker
   docker system prune -a
   
   # Rebuild containers
   docker-compose down
   docker-compose build --no-cache
   docker-compose up -d
   ```

### Log Locations

- Backend logs: `backend/logs/`
- Docker logs: `docker-compose logs service-name`
- System logs: `/var/log/`

## 📞 Support

For deployment issues:
- Check the [GitHub Issues](https://github.com/HectorTa1989/proxyforge/issues)
- Review the [Documentation](https://docs.proxyforge.io)
- Contact support: <EMAIL>

---

**Built with ❤️ by [HectorTa1989](https://github.com/HectorTa1989)**
