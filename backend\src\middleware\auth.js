const jwt = require('jsonwebtoken');
const { User } = require('../models');
const logger = require('../utils/logger');

// JWT secret from environment variables
const JWT_SECRET = process.env.JWT_SECRET || 'your-super-secret-jwt-key-change-in-production';
const JWT_EXPIRES_IN = process.env.JWT_EXPIRES_IN || '24h';

// Generate JWT token
const generateToken = (user) => {
  const payload = {
    id: user.id,
    email: user.email,
    subscriptionTier: user.subscriptionTier,
    role: user.role
  };

  return jwt.sign(payload, JWT_SECRET, {
    expiresIn: JWT_EXPIRES_IN,
    issuer: 'proxyforge-api',
    audience: 'proxyforge-client'
  });
};

// Generate refresh token
const generateRefreshToken = (user) => {
  const payload = {
    id: user.id,
    type: 'refresh'
  };

  return jwt.sign(payload, JWT_SECRET, {
    expiresIn: '7d', // Refresh tokens last 7 days
    issuer: 'proxyforge-api',
    audience: 'proxyforge-client'
  });
};

// Verify JWT token
const verifyToken = (token) => {
  try {
    return jwt.verify(token, JWT_SECRET, {
      issuer: 'proxyforge-api',
      audience: 'proxyforge-client'
    });
  } catch (error) {
    throw new Error('Invalid token');
  }
};

// Middleware to authenticate JWT token
const authenticateToken = async (req, res, next) => {
  try {
    const authHeader = req.headers.authorization;
    const token = authHeader && authHeader.split(' ')[1]; // Bearer TOKEN

    if (!token) {
      return res.status(401).json({
        error: 'Access token required',
        message: 'Please provide a valid access token'
      });
    }

    // Verify token
    const decoded = verifyToken(token);
    
    // Check if token is a refresh token (not allowed for API access)
    if (decoded.type === 'refresh') {
      return res.status(401).json({
        error: 'Invalid token type',
        message: 'Refresh tokens cannot be used for API access'
      });
    }

    // Get user from database
    const user = await User.findByPk(decoded.id);
    
    if (!user) {
      return res.status(401).json({
        error: 'User not found',
        message: 'The user associated with this token no longer exists'
      });
    }

    if (!user.isActive) {
      return res.status(401).json({
        error: 'Account deactivated',
        message: 'Your account has been deactivated'
      });
    }

    // Add user to request object
    req.user = {
      id: user.id,
      email: user.email,
      subscriptionTier: user.subscriptionTier,
      role: user.role,
      isActive: user.isActive
    };

    // Log successful authentication
    logger.debug(`User ${user.id} authenticated successfully`);

    next();
  } catch (error) {
    logger.warn('Authentication failed:', error.message);
    
    if (error.name === 'TokenExpiredError') {
      return res.status(401).json({
        error: 'Token expired',
        message: 'Your access token has expired, please refresh'
      });
    }
    
    if (error.name === 'JsonWebTokenError') {
      return res.status(401).json({
        error: 'Invalid token',
        message: 'The provided token is invalid'
      });
    }

    return res.status(401).json({
      error: 'Authentication failed',
      message: 'Unable to authenticate request'
    });
  }
};

// Optional authentication middleware (doesn't fail if no token)
const optionalAuth = async (req, res, next) => {
  try {
    const authHeader = req.headers.authorization;
    const token = authHeader && authHeader.split(' ')[1];

    if (!token) {
      req.user = null;
      return next();
    }

    const decoded = verifyToken(token);
    const user = await User.findByPk(decoded.id);
    
    if (user && user.isActive) {
      req.user = {
        id: user.id,
        email: user.email,
        subscriptionTier: user.subscriptionTier,
        role: user.role,
        isActive: user.isActive
      };
    } else {
      req.user = null;
    }

    next();
  } catch (error) {
    // Don't fail on optional auth errors
    req.user = null;
    next();
  }
};

// Middleware to check user roles
const requireRole = (...roles) => {
  return (req, res, next) => {
    if (!req.user) {
      return res.status(401).json({
        error: 'Authentication required',
        message: 'Please authenticate to access this resource'
      });
    }

    if (!roles.includes(req.user.role)) {
      return res.status(403).json({
        error: 'Insufficient permissions',
        message: `This resource requires one of the following roles: ${roles.join(', ')}`
      });
    }

    next();
  };
};

// Middleware to check subscription tier
const requireSubscription = (...tiers) => {
  return (req, res, next) => {
    if (!req.user) {
      return res.status(401).json({
        error: 'Authentication required',
        message: 'Please authenticate to access this resource'
      });
    }

    const tierHierarchy = {
      'free': 0,
      'pro': 1,
      'business': 2,
      'enterprise': 3
    };

    const userTierLevel = tierHierarchy[req.user.subscriptionTier] || 0;
    const requiredLevel = Math.min(...tiers.map(tier => tierHierarchy[tier] || 0));

    if (userTierLevel < requiredLevel) {
      return res.status(403).json({
        error: 'Subscription upgrade required',
        message: `This feature requires a ${tiers.join(' or ')} subscription`,
        currentTier: req.user.subscriptionTier,
        upgradeUrl: '/pricing'
      });
    }

    next();
  };
};

// Middleware to check if user owns resource
const requireOwnership = (resourceIdParam = 'id') => {
  return async (req, res, next) => {
    try {
      const resourceId = req.params[resourceIdParam];
      const userId = req.user.id;

      // This is a generic ownership check
      // In practice, you'd check against specific models
      if (resourceId !== userId && req.user.role !== 'admin') {
        return res.status(403).json({
          error: 'Access denied',
          message: 'You can only access your own resources'
        });
      }

      next();
    } catch (error) {
      logger.error('Ownership check error:', error);
      return res.status(500).json({
        error: 'Authorization error',
        message: 'Unable to verify resource ownership'
      });
    }
  };
};

// Middleware to refresh token
const refreshToken = async (req, res, next) => {
  try {
    const { refreshToken } = req.body;

    if (!refreshToken) {
      return res.status(400).json({
        error: 'Refresh token required',
        message: 'Please provide a refresh token'
      });
    }

    const decoded = verifyToken(refreshToken);
    
    if (decoded.type !== 'refresh') {
      return res.status(400).json({
        error: 'Invalid token type',
        message: 'Only refresh tokens can be used for token refresh'
      });
    }

    const user = await User.findByPk(decoded.id);
    
    if (!user || !user.isActive) {
      return res.status(401).json({
        error: 'Invalid refresh token',
        message: 'The refresh token is invalid or user is inactive'
      });
    }

    // Generate new tokens
    const newAccessToken = generateToken(user);
    const newRefreshToken = generateRefreshToken(user);

    res.json({
      accessToken: newAccessToken,
      refreshToken: newRefreshToken,
      user: {
        id: user.id,
        email: user.email,
        subscriptionTier: user.subscriptionTier,
        role: user.role
      }
    });

  } catch (error) {
    logger.error('Token refresh error:', error);
    
    if (error.name === 'TokenExpiredError') {
      return res.status(401).json({
        error: 'Refresh token expired',
        message: 'Please log in again'
      });
    }

    return res.status(401).json({
      error: 'Token refresh failed',
      message: 'Unable to refresh token'
    });
  }
};

module.exports = {
  generateToken,
  generateRefreshToken,
  verifyToken,
  authenticateToken,
  optionalAuth,
  requireRole,
  requireSubscription,
  requireOwnership,
  refreshToken
};
