const axios = require('axios');

// Serverless function for proxy connection
exports.handler = async (event, context) => {
  // Set CORS headers
  const headers = {
    'Access-Control-Allow-Origin': '*',
    'Access-Control-Allow-Headers': 'Content-Type, Authorization',
    'Access-Control-Allow-Methods': 'GET, POST, PUT, DELETE, OPTIONS',
    'Content-Type': 'application/json'
  };

  // Handle preflight requests
  if (event.httpMethod === 'OPTIONS') {
    return {
      statusCode: 200,
      headers,
      body: ''
    };
  }

  try {
    // Only allow POST requests
    if (event.httpMethod !== 'POST') {
      return {
        statusCode: 405,
        headers,
        body: JSON.stringify({ error: 'Method not allowed' })
      };
    }

    // Parse request body
    const { country, proxyType = 'http' } = JSON.parse(event.body || '{}');

    // Validate input
    if (country && !/^[A-Z]{2}$/.test(country)) {
      return {
        statusCode: 400,
        headers,
        body: JSON.stringify({ error: 'Invalid country code' })
      };
    }

    // Get user info from headers (in a real app, validate JWT token)
    const authHeader = event.headers.authorization;
    if (!authHeader || !authHeader.startsWith('Bearer ')) {
      return {
        statusCode: 401,
        headers,
        body: JSON.stringify({ error: 'Unauthorized' })
      };
    }

    // Simulate proxy selection logic
    const availableProxies = [
      { id: '1', host: '*************', port: 8080, country: 'US', type: 'http', speed: 95 },
      { id: '2', host: '*************', port: 8080, country: 'GB', type: 'http', speed: 87 },
      { id: '3', host: '*************', port: 8080, country: 'DE', type: 'http', speed: 92 },
      { id: '4', host: '*************', port: 8080, country: 'JP', type: 'http', speed: 78 },
      { id: '5', host: '*************', port: 8080, country: 'CA', type: 'http', speed: 89 }
    ];

    // Filter by country if specified
    let filteredProxies = country 
      ? availableProxies.filter(p => p.country === country)
      : availableProxies;

    // Filter by type
    filteredProxies = filteredProxies.filter(p => p.type === proxyType);

    if (filteredProxies.length === 0) {
      return {
        statusCode: 503,
        headers,
        body: JSON.stringify({ 
          error: 'No proxy servers available',
          message: 'Please try a different location or proxy type'
        })
      };
    }

    // Select best proxy (highest speed)
    const selectedProxy = filteredProxies.sort((a, b) => b.speed - a.speed)[0];

    // Get geo information for the proxy
    let geoInfo = {};
    try {
      const geoResponse = await axios.get(`http://ip-api.com/json/${selectedProxy.host}`, {
        timeout: 3000
      });
      
      if (geoResponse.data.status === 'success') {
        geoInfo = {
          country: geoResponse.data.country,
          countryCode: geoResponse.data.countryCode,
          city: geoResponse.data.city,
          region: geoResponse.data.regionName,
          timezone: geoResponse.data.timezone,
          isp: geoResponse.data.isp
        };
      }
    } catch (geoError) {
      console.warn('Failed to get geo info:', geoError.message);
      // Use fallback geo info
      geoInfo = {
        country: getCountryName(selectedProxy.country),
        countryCode: selectedProxy.country,
        city: 'Unknown',
        region: 'Unknown',
        timezone: 'UTC',
        isp: 'ProxyForge Network'
      };
    }

    // Simulate connection establishment
    const connectionId = generateConnectionId();
    
    // Return connection details
    return {
      statusCode: 200,
      headers,
      body: JSON.stringify({
        success: true,
        connectionId,
        connection: {
          id: selectedProxy.id,
          proxyHost: selectedProxy.host,
          proxyPort: selectedProxy.port,
          proxyType: selectedProxy.type,
          country: geoInfo.country,
          countryCode: geoInfo.countryCode,
          city: geoInfo.city,
          region: geoInfo.region,
          timezone: geoInfo.timezone,
          isp: geoInfo.isp,
          speed: selectedProxy.speed,
          reliability: 0.95
        },
        instructions: {
          http: `Configure your browser to use HTTP proxy: ${selectedProxy.host}:${selectedProxy.port}`,
          https: `Configure your browser to use HTTPS proxy: ${selectedProxy.host}:${selectedProxy.port}`,
          note: 'This is a demo implementation. In production, you would configure actual proxy servers.'
        },
        timestamp: new Date().toISOString()
      })
    };

  } catch (error) {
    console.error('Proxy connection error:', error);
    
    return {
      statusCode: 500,
      headers,
      body: JSON.stringify({
        error: 'Internal server error',
        message: 'Failed to establish proxy connection'
      })
    };
  }
};

// Helper functions
function generateConnectionId() {
  return 'conn_' + Date.now().toString(36) + Math.random().toString(36).substr(2);
}

function getCountryName(code) {
  const countries = {
    'US': 'United States',
    'GB': 'United Kingdom',
    'DE': 'Germany',
    'JP': 'Japan',
    'CA': 'Canada',
    'FR': 'France',
    'AU': 'Australia',
    'NL': 'Netherlands',
    'SG': 'Singapore',
    'BR': 'Brazil'
  };
  
  return countries[code] || 'Unknown';
}
