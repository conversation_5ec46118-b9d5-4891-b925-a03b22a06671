const rateLimit = require('express-rate-limit');
const RedisStore = require('rate-limit-redis');
const Redis = require('ioredis');
const logger = require('../utils/logger');

// Redis client for rate limiting
let redisClient = null;

if (process.env.REDIS_URL) {
  try {
    redisClient = new Redis(process.env.REDIS_URL);
    logger.info('Connected to Redis for rate limiting');
  } catch (error) {
    logger.warn('Failed to connect to Redis, using memory store for rate limiting:', error.message);
  }
}

// Create rate limiter store
const store = redisClient ? new RedisStore({
  sendCommand: (...args) => redisClient.call(...args),
}) : undefined;

// General API rate limiter
const apiLimiter = rateLimit({
  store,
  windowMs: 15 * 60 * 1000, // 15 minutes
  max: 100, // limit each IP to 100 requests per windowMs
  message: {
    error: 'Too many requests from this IP, please try again later.',
    retryAfter: '15 minutes'
  },
  standardHeaders: true,
  legacyHeaders: false,
  keyGenerator: (req) => {
    // Use user ID if authenticated, otherwise IP
    return req.user?.id || req.ip;
  },
  skip: (req) => {
    // Skip rate limiting for health checks
    return req.path === '/health';
  },
  onLimitReached: (req, res, options) => {
    logger.warn(`Rate limit exceeded for ${req.user?.id || req.ip} on ${req.path}`);
  }
});

// Authentication rate limiter (stricter)
const authLimiter = rateLimit({
  store,
  windowMs: 15 * 60 * 1000, // 15 minutes
  max: 5, // limit each IP to 5 auth requests per windowMs
  message: {
    error: 'Too many authentication attempts, please try again later.',
    retryAfter: '15 minutes'
  },
  standardHeaders: true,
  legacyHeaders: false,
  skipSuccessfulRequests: true, // Don't count successful requests
  onLimitReached: (req, res, options) => {
    logger.warn(`Auth rate limit exceeded for IP ${req.ip}`);
  }
});

// Proxy connection rate limiter (per user)
const proxyLimiter = rateLimit({
  store,
  windowMs: 60 * 1000, // 1 minute
  max: (req) => {
    // Different limits based on user subscription
    const userTier = req.user?.subscriptionTier || 'free';
    switch (userTier) {
      case 'enterprise':
        return 1000;
      case 'business':
        return 500;
      case 'pro':
        return 200;
      case 'free':
      default:
        return 50;
    }
  },
  message: (req) => {
    const userTier = req.user?.subscriptionTier || 'free';
    return {
      error: 'Proxy connection rate limit exceeded for your subscription tier.',
      currentTier: userTier,
      upgradeUrl: '/pricing'
    };
  },
  keyGenerator: (req) => {
    return `proxy:${req.user?.id || req.ip}`;
  },
  onLimitReached: (req, res, options) => {
    logger.warn(`Proxy rate limit exceeded for user ${req.user?.id} (${req.user?.subscriptionTier})`);
  }
});

// File upload rate limiter
const uploadLimiter = rateLimit({
  store,
  windowMs: 60 * 60 * 1000, // 1 hour
  max: 10, // limit each user to 10 uploads per hour
  message: {
    error: 'Too many file uploads, please try again later.',
    retryAfter: '1 hour'
  },
  keyGenerator: (req) => {
    return `upload:${req.user?.id || req.ip}`;
  }
});

// API endpoint specific limiters
const endpointLimiters = {
  // User registration - very strict
  register: rateLimit({
    store,
    windowMs: 60 * 60 * 1000, // 1 hour
    max: 3, // 3 registrations per hour per IP
    message: {
      error: 'Too many registration attempts, please try again later.',
      retryAfter: '1 hour'
    }
  }),

  // Password reset - strict
  passwordReset: rateLimit({
    store,
    windowMs: 60 * 60 * 1000, // 1 hour
    max: 5, // 5 password reset attempts per hour
    message: {
      error: 'Too many password reset attempts, please try again later.',
      retryAfter: '1 hour'
    }
  }),

  // Contact form - moderate
  contact: rateLimit({
    store,
    windowMs: 60 * 60 * 1000, // 1 hour
    max: 5, // 5 contact form submissions per hour
    message: {
      error: 'Too many contact form submissions, please try again later.',
      retryAfter: '1 hour'
    }
  }),

  // Analytics - lenient for dashboard updates
  analytics: rateLimit({
    store,
    windowMs: 60 * 1000, // 1 minute
    max: 60, // 60 requests per minute
    message: {
      error: 'Too many analytics requests, please slow down.',
      retryAfter: '1 minute'
    }
  })
};

// Dynamic rate limiter based on user subscription
const createUserBasedLimiter = (baseLimit, windowMs = 15 * 60 * 1000) => {
  return rateLimit({
    store,
    windowMs,
    max: (req) => {
      const userTier = req.user?.subscriptionTier || 'free';
      const multiplier = {
        enterprise: 10,
        business: 5,
        pro: 3,
        free: 1
      }[userTier] || 1;
      
      return baseLimit * multiplier;
    },
    keyGenerator: (req) => {
      return `user:${req.user?.id || req.ip}`;
    },
    message: (req) => {
      const userTier = req.user?.subscriptionTier || 'free';
      return {
        error: 'Rate limit exceeded for your subscription tier.',
        currentTier: userTier,
        upgradeUrl: '/pricing'
      };
    }
  });
};

// Burst protection - very short window, high limit
const burstLimiter = rateLimit({
  store,
  windowMs: 1000, // 1 second
  max: 10, // 10 requests per second
  message: {
    error: 'Too many requests in a short time, please slow down.',
    retryAfter: '1 second'
  },
  standardHeaders: false, // Don't send standard headers for burst limiting
  legacyHeaders: false
});

// Middleware to apply multiple rate limiters
const applyRateLimiters = (...limiters) => {
  return (req, res, next) => {
    let index = 0;
    
    const applyNext = (err) => {
      if (err) return next(err);
      
      if (index >= limiters.length) {
        return next();
      }
      
      const limiter = limiters[index++];
      limiter(req, res, applyNext);
    };
    
    applyNext();
  };
};

// Export rate limiters
module.exports = {
  apiLimiter,
  authLimiter,
  proxyLimiter,
  uploadLimiter,
  endpointLimiters,
  createUserBasedLimiter,
  burstLimiter,
  applyRateLimiters
};
