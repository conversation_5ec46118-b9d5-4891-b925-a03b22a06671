const express = require('express');
const bcrypt = require('bcryptjs');
const { body, validationResult } = require('express-validator');
const { User, Connection } = require('../models');
const { createUserBasedLimiter } = require('../middleware/rateLimiter');
const { requireOwnership } = require('../middleware/auth');
const logger = require('../utils/logger');

const router = express.Router();

// Validation middleware
const validateProfileUpdate = [
  body('firstName')
    .optional()
    .trim()
    .isLength({ min: 1, max: 50 })
    .withMessage('First name must be between 1 and 50 characters'),
  body('lastName')
    .optional()
    .trim()
    .isLength({ min: 1, max: 50 })
    .withMessage('Last name must be between 1 and 50 characters'),
  body('email')
    .optional()
    .isEmail()
    .normalizeEmail()
    .withMessage('Please provide a valid email address')
];

const validatePasswordChange = [
  body('currentPassword')
    .notEmpty()
    .withMessage('Current password is required'),
  body('newPassword')
    .isLength({ min: 8 })
    .withMessage('New password must be at least 8 characters long')
    .matches(/^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[@$!%*?&])[A-Za-z\d@$!%*?&]/)
    .withMessage('New password must contain at least one uppercase letter, one lowercase letter, one number, and one special character')
];

// Helper function to handle validation errors
const handleValidationErrors = (req, res, next) => {
  const errors = validationResult(req);
  if (!errors.isEmpty()) {
    return res.status(400).json({
      error: 'Validation failed',
      details: errors.array()
    });
  }
  next();
};

// GET /api/user/profile - Get user profile
router.get('/profile',
  createUserBasedLimiter(30),
  async (req, res) => {
    try {
      const userId = req.user.id;

      const user = await User.findByPk(userId, {
        attributes: { exclude: ['password'] }
      });

      if (!user) {
        return res.status(404).json({
          error: 'User not found',
          message: 'User profile not found'
        });
      }

      // Get subscription info
      const subscriptionInfo = {
        tier: user.subscriptionTier,
        isActive: true,
        expiresAt: user.subscriptionExpiresAt,
        daysRemaining: user.subscriptionExpiresAt 
          ? Math.ceil((new Date(user.subscriptionExpiresAt) - new Date()) / (1000 * 60 * 60 * 24))
          : null
      };

      // Get usage statistics
      const usageStats = await Connection.findAll({
        where: { 
          userId,
          createdAt: {
            [Connection.sequelize.Op.gte]: new Date(Date.now() - 30 * 24 * 60 * 60 * 1000) // Last 30 days
          }
        },
        attributes: [
          [Connection.sequelize.fn('COUNT', '*'), 'totalConnections'],
          [Connection.sequelize.fn('SUM', Connection.sequelize.col('bytesTransferred')), 'totalBytes']
        ],
        raw: true
      });

      res.json({
        user: {
          id: user.id,
          email: user.email,
          firstName: user.firstName,
          lastName: user.lastName,
          subscriptionTier: user.subscriptionTier,
          role: user.role,
          isActive: user.isActive,
          isEmailVerified: user.isEmailVerified,
          lastLoginAt: user.lastLoginAt,
          createdAt: user.createdAt,
          preferences: user.preferences
        },
        subscription: subscriptionInfo,
        usage: {
          totalConnections: parseInt(usageStats[0].totalConnections) || 0,
          totalBytesTransferred: parseInt(usageStats[0].totalBytes) || 0
        }
      });

    } catch (error) {
      logger.error('Error fetching user profile:', error);
      res.status(500).json({
        error: 'Unable to fetch profile',
        message: 'Please try again later'
      });
    }
  }
);

// PUT /api/user/profile - Update user profile
router.put('/profile',
  createUserBasedLimiter(10),
  validateProfileUpdate,
  handleValidationErrors,
  async (req, res) => {
    try {
      const userId = req.user.id;
      const { firstName, lastName, email } = req.body;

      const user = await User.findByPk(userId);
      if (!user) {
        return res.status(404).json({
          error: 'User not found',
          message: 'User profile not found'
        });
      }

      // Check if email is already taken by another user
      if (email && email !== user.email) {
        const existingUser = await User.findOne({ 
          where: { 
            email,
            id: { [User.sequelize.Op.ne]: userId }
          }
        });
        
        if (existingUser) {
          return res.status(409).json({
            error: 'Email already taken',
            message: 'This email address is already associated with another account'
          });
        }
      }

      // Update user profile
      const updateData = {};
      if (firstName !== undefined) updateData.firstName = firstName;
      if (lastName !== undefined) updateData.lastName = lastName;
      if (email !== undefined) {
        updateData.email = email;
        updateData.isEmailVerified = false; // Reset email verification
      }

      await user.update(updateData);

      logger.logAuth('Profile updated', userId, { updatedFields: Object.keys(updateData) });

      res.json({
        message: 'Profile updated successfully',
        user: {
          id: user.id,
          email: user.email,
          firstName: user.firstName,
          lastName: user.lastName,
          subscriptionTier: user.subscriptionTier,
          role: user.role,
          isActive: user.isActive,
          isEmailVerified: user.isEmailVerified
        }
      });

    } catch (error) {
      logger.error('Error updating user profile:', error);
      res.status(500).json({
        error: 'Profile update failed',
        message: 'Unable to update profile. Please try again.'
      });
    }
  }
);

// PUT /api/user/password - Change password
router.put('/password',
  createUserBasedLimiter(5),
  validatePasswordChange,
  handleValidationErrors,
  async (req, res) => {
    try {
      const userId = req.user.id;
      const { currentPassword, newPassword } = req.body;

      const user = await User.findByPk(userId);
      if (!user) {
        return res.status(404).json({
          error: 'User not found',
          message: 'User not found'
        });
      }

      // Verify current password
      const isCurrentPasswordValid = await bcrypt.compare(currentPassword, user.password);
      if (!isCurrentPasswordValid) {
        logger.logSecurity('Invalid current password in password change', { userId });
        return res.status(400).json({
          error: 'Invalid current password',
          message: 'The current password you entered is incorrect'
        });
      }

      // Check if new password is different from current
      const isSamePassword = await bcrypt.compare(newPassword, user.password);
      if (isSamePassword) {
        return res.status(400).json({
          error: 'Same password',
          message: 'New password must be different from current password'
        });
      }

      // Hash new password
      const saltRounds = 12;
      const hashedNewPassword = await bcrypt.hash(newPassword, saltRounds);

      // Update password
      await user.update({ password: hashedNewPassword });

      logger.logAuth('Password changed', userId);

      res.json({
        message: 'Password changed successfully'
      });

    } catch (error) {
      logger.error('Error changing password:', error);
      res.status(500).json({
        error: 'Password change failed',
        message: 'Unable to change password. Please try again.'
      });
    }
  }
);

// GET /api/user/preferences - Get user preferences
router.get('/preferences',
  createUserBasedLimiter(20),
  async (req, res) => {
    try {
      const userId = req.user.id;

      const user = await User.findByPk(userId, {
        attributes: ['preferences']
      });

      if (!user) {
        return res.status(404).json({
          error: 'User not found',
          message: 'User not found'
        });
      }

      res.json({
        preferences: user.preferences || {}
      });

    } catch (error) {
      logger.error('Error fetching user preferences:', error);
      res.status(500).json({
        error: 'Unable to fetch preferences',
        message: 'Please try again later'
      });
    }
  }
);

// PUT /api/user/preferences - Update user preferences
router.put('/preferences',
  createUserBasedLimiter(10),
  async (req, res) => {
    try {
      const userId = req.user.id;
      const { preferences } = req.body;

      if (!preferences || typeof preferences !== 'object') {
        return res.status(400).json({
          error: 'Invalid preferences',
          message: 'Preferences must be a valid object'
        });
      }

      const user = await User.findByPk(userId);
      if (!user) {
        return res.status(404).json({
          error: 'User not found',
          message: 'User not found'
        });
      }

      // Merge with existing preferences
      const updatedPreferences = {
        ...user.preferences,
        ...preferences
      };

      await user.update({ preferences: updatedPreferences });

      logger.info(`User ${userId} updated preferences`);

      res.json({
        message: 'Preferences updated successfully',
        preferences: updatedPreferences
      });

    } catch (error) {
      logger.error('Error updating user preferences:', error);
      res.status(500).json({
        error: 'Preferences update failed',
        message: 'Unable to update preferences. Please try again.'
      });
    }
  }
);

// GET /api/user/usage - Get usage statistics
router.get('/usage',
  createUserBasedLimiter(20),
  async (req, res) => {
    try {
      const userId = req.user.id;
      const { period = '30d' } = req.query;

      // Calculate date range
      let startDate;
      const now = new Date();
      
      switch (period) {
        case '24h':
          startDate = new Date(now.getTime() - 24 * 60 * 60 * 1000);
          break;
        case '7d':
          startDate = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000);
          break;
        case '30d':
          startDate = new Date(now.getTime() - 30 * 24 * 60 * 60 * 1000);
          break;
        case '90d':
          startDate = new Date(now.getTime() - 90 * 24 * 60 * 60 * 1000);
          break;
        default:
          startDate = new Date(now.getTime() - 30 * 24 * 60 * 60 * 1000);
      }

      // Get usage statistics
      const stats = await Connection.findAll({
        where: {
          userId,
          createdAt: {
            [Connection.sequelize.Op.gte]: startDate
          }
        },
        attributes: [
          [Connection.sequelize.fn('COUNT', '*'), 'totalConnections'],
          [Connection.sequelize.fn('SUM', Connection.sequelize.col('bytesTransferred')), 'totalBytes'],
          [Connection.sequelize.fn('AVG', Connection.sequelize.col('responseTime')), 'avgResponseTime'],
          [Connection.sequelize.fn('COUNT', Connection.sequelize.literal('CASE WHEN statusCode >= 200 AND statusCode < 300 THEN 1 END')), 'successfulConnections']
        ],
        raw: true
      });

      // Get daily breakdown
      const dailyStats = await Connection.findAll({
        where: {
          userId,
          createdAt: {
            [Connection.sequelize.Op.gte]: startDate
          }
        },
        attributes: [
          [Connection.sequelize.fn('DATE', Connection.sequelize.col('createdAt')), 'date'],
          [Connection.sequelize.fn('COUNT', '*'), 'connections'],
          [Connection.sequelize.fn('SUM', Connection.sequelize.col('bytesTransferred')), 'bytes']
        ],
        group: [Connection.sequelize.fn('DATE', Connection.sequelize.col('createdAt'))],
        order: [[Connection.sequelize.fn('DATE', Connection.sequelize.col('createdAt')), 'ASC']],
        raw: true
      });

      const result = {
        period,
        summary: {
          totalConnections: parseInt(stats[0].totalConnections) || 0,
          totalBytesTransferred: parseInt(stats[0].totalBytes) || 0,
          avgResponseTime: Math.round(parseFloat(stats[0].avgResponseTime)) || 0,
          successRate: stats[0].totalConnections > 0 
            ? Math.round((stats[0].successfulConnections / stats[0].totalConnections) * 100)
            : 0
        },
        daily: dailyStats.map(day => ({
          date: day.date,
          connections: parseInt(day.connections),
          bytes: parseInt(day.bytes) || 0
        }))
      };

      res.json(result);

    } catch (error) {
      logger.error('Error fetching usage statistics:', error);
      res.status(500).json({
        error: 'Unable to fetch usage statistics',
        message: 'Please try again later'
      });
    }
  }
);

// DELETE /api/user/account - Delete user account
router.delete('/account',
  createUserBasedLimiter(2), // Very limited
  async (req, res) => {
    try {
      const userId = req.user.id;
      const { confirmPassword } = req.body;

      if (!confirmPassword) {
        return res.status(400).json({
          error: 'Password confirmation required',
          message: 'Please provide your password to confirm account deletion'
        });
      }

      const user = await User.findByPk(userId);
      if (!user) {
        return res.status(404).json({
          error: 'User not found',
          message: 'User not found'
        });
      }

      // Verify password
      const isPasswordValid = await bcrypt.compare(confirmPassword, user.password);
      if (!isPasswordValid) {
        logger.logSecurity('Invalid password in account deletion attempt', { userId });
        return res.status(400).json({
          error: 'Invalid password',
          message: 'The password you entered is incorrect'
        });
      }

      // Soft delete - deactivate account instead of hard delete
      await user.update({ 
        isActive: false,
        email: `deleted_${Date.now()}_${user.email}` // Prevent email conflicts
      });

      logger.logAuth('Account deleted', userId);

      res.json({
        message: 'Account has been deactivated successfully'
      });

    } catch (error) {
      logger.error('Error deleting user account:', error);
      res.status(500).json({
        error: 'Account deletion failed',
        message: 'Unable to delete account. Please try again.'
      });
    }
  }
);

module.exports = router;
