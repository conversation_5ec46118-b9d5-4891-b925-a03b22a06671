import React from 'react';
import { <PERSON>, Card<PERSON>ontent, Card<PERSON>eader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Shield, Users, Globe, Zap, Award, Clock } from 'lucide-react';
import { motion } from 'framer-motion';

export default function About() {
  const milestones = [
    { year: '2020', event: 'GEOSHIFT FOUNDED', color: '#FF0066' },
    { year: '2021', event: '10K USERS MILESTONE', color: '#00FF66' },
    { year: '2022', event: '50+ COUNTRIES ADDED', color: '#0066FF' },
    { year: '2023', event: 'ENTERPRISE LAUNCH', color: '#FFFF00' },
    { year: '2024', event: 'PRO VERSION RELEASED', color: '#FF6600' }
  ];

  const team = [
    { name: 'ALEX CIPHER', role: 'CEO & FOUNDER', specialty: 'NETWORK SECURITY' },
    { name: 'SARAH PROXY', role: 'CTO', specialty: 'INFRASTRUCTURE' },
    { name: 'MIKE TUNNEL', role: 'HEAD OF ENGINEERING', specialty: 'PROTOCOL DESIGN' },
    { name: 'JENNY ENCRYPT', role: 'SECURITY LEAD', specialty: 'CRYPTOGRAPHY' }
  ];

  return (
    <div className="min-h-screen bg-white p-6 font-mono">
      <div className="max-w-6xl mx-auto">
        {/* Header */}
        <div className="mb-12 text-center">
          <h1 className="text-6xl font-black text-black mb-4 transform -rotate-2">
            ABOUT GEOSHIFT PRO
          </h1>
          <p className="text-2xl text-black font-bold transform rotate-1">
            THE STORY BEHIND THE BRUTAL REVOLUTION
          </p>
        </div>

        {/* Mission Statement */}
        <Card className="border-4 border-black shadow-[12px_12px_0px_0px_#FF0066] mb-12 bg-white transform rotate-1">
          <CardHeader className="border-b-4 border-black bg-[#FF0066] text-white">
            <CardTitle className="text-3xl font-black text-center">
              OUR BRUTAL MISSION
            </CardTitle>
          </CardHeader>
          <CardContent className="p-8">
            <p className="text-xl font-bold text-center leading-relaxed">
              TO DEMOCRATIZE DIGITAL FREEDOM BY PROVIDING MILITARY-GRADE IP LOCATION SWITCHING 
              WITH A BRUTALLY HONEST, NO-BULLSH*T APPROACH TO PRIVACY AND SECURITY.
            </p>
          </CardContent>
        </Card>

        {/* Stats Grid */}
        <div className="grid grid-cols-2 lg:grid-cols-4 gap-6 mb-12">
          {[
            { icon: Users, number: '50K+', label: 'ACTIVE USERS', color: '#00FF66' },
            { icon: Globe, number: '85+', label: 'COUNTRIES', color: '#0066FF' },
            { icon: Shield, number: '99.9%', label: 'UPTIME', color: '#FF0066' },
            { icon: Zap, number: '200+', label: 'MBPS SPEED', color: '#FFFF00' }
          ].map((stat, index) => (
            <motion.div
              key={stat.label}
              initial={{ opacity: 0, scale: 0.8 }}
              animate={{ opacity: 1, scale: 1 }}
              transition={{ delay: index * 0.2 }}
            >
              <Card 
                className="border-4 border-black shadow-[6px_6px_0px_0px_#000000] bg-white text-center transform hover:rotate-2 transition-transform"
                style={{ borderColor: stat.color }}
              >
                <CardContent className="p-6">
                  <div 
                    className="w-16 h-16 mx-auto mb-4 border-4 border-black flex items-center justify-center transform -rotate-12"
                    style={{ backgroundColor: stat.color }}
                  >
                    <stat.icon className="w-8 h-8 text-white" />
                  </div>
                  <p className="text-3xl font-black mb-2">{stat.number}</p>
                  <p className="text-sm font-bold text-gray-600">{stat.label}</p>
                </CardContent>
              </Card>
            </motion.div>
          ))}
        </div>

        {/* Timeline */}
        <Card className="border-4 border-black shadow-[8px_8px_0px_0px_#00FF66] mb-12 bg-white transform -rotate-1">
          <CardHeader className="border-b-4 border-black bg-[#00FF66] text-black">
            <CardTitle className="text-3xl font-black text-center">
              BRUTAL TIMELINE
            </CardTitle>
          </CardHeader>
          <CardContent className="p-8">
            <div className="space-y-6">
              {milestones.map((milestone, index) => (
                <motion.div
                  key={milestone.year}
                  initial={{ opacity: 0, x: -50 }}
                  animate={{ opacity: 1, x: 0 }}
                  transition={{ delay: index * 0.2 }}
                  className="flex items-center gap-6"
                >
                  <Badge 
                    className="text-2xl px-6 py-3 border-4 border-black font-black"
                    style={{ backgroundColor: milestone.color, color: milestone.color === '#FFFF00' ? 'black' : 'white' }}
                  >
                    {milestone.year}
                  </Badge>
                  <div className="flex-1 p-4 border-4 border-black bg-black text-white font-bold transform hover:rotate-1 transition-transform">
                    {milestone.event}
                  </div>
                </motion.div>
              ))}
            </div>
          </CardContent>
        </Card>

        {/* Team Section */}
        <Card className="border-4 border-black shadow-[8px_8px_0px_0px_#0066FF] mb-12 bg-white transform rotate-0.5">
          <CardHeader className="border-b-4 border-black bg-[#0066FF] text-white">
            <CardTitle className="text-3xl font-black text-center">
              THE BRUTAL TEAM
            </CardTitle>
          </CardHeader>
          <CardContent className="p-8">
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
              {team.map((member, index) => (
                <motion.div
                  key={member.name}
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ delay: index * 0.2 }}
                  className="text-center p-6 border-4 border-black bg-black text-white transform hover:-rotate-2 transition-transform"
                >
                  <div className="w-20 h-20 mx-auto mb-4 bg-[#FF0066] border-4 border-white rounded-full flex items-center justify-center">
                    <span className="text-2xl font-black">
                      {member.name.split(' ').map(n => n[0]).join('')}
                    </span>
                  </div>
                  <h3 className="text-lg font-black mb-2">{member.name}</h3>
                  <p className="text-[#00FF66] font-bold mb-2">{member.role}</p>
                  <p className="text-sm text-[#FFFF00] font-bold">{member.specialty}</p>
                </motion.div>
              ))}
            </div>
          </CardContent>
        </Card>

        {/* Values Section */}
        <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
          {[
            {
              title: 'TRANSPARENCY',
              description: 'NO HIDDEN FEES, NO BULLSH*T, NO COMPROMISES',
              color: '#FF0066',
              icon: Shield
            },
            {
              title: 'PERFORMANCE',
              description: 'FASTEST SERVERS, LOWEST LATENCY, HIGHEST UPTIME',
              color: '#00FF66',
              icon: Zap
            },
            {
              title: 'INNOVATION',
              description: 'CUTTING-EDGE TECHNOLOGY, CONSTANT IMPROVEMENT',
              color: '#0066FF',
              icon: Award
            }
          ].map((value, index) => (
            <motion.div
              key={value.title}
              initial={{ opacity: 0, scale: 0.8 }}
              animate={{ opacity: 1, scale: 1 }}
              transition={{ delay: index * 0.3 }}
            >
              <Card 
                className="border-4 border-black shadow-[6px_6px_0px_0px_#000000] bg-white text-center transform hover:rotate-1 transition-transform h-full"
                style={{ borderColor: value.color }}
              >
                <CardContent className="p-6">
                  <div 
                    className="w-20 h-20 mx-auto mb-6 border-4 border-black flex items-center justify-center transform -rotate-12"
                    style={{ backgroundColor: value.color }}
                  >
                    <value.icon className="w-10 h-10 text-white" />
                  </div>
                  <h3 className="text-2xl font-black mb-4">{value.title}</h3>
                  <p className="font-bold text-gray-600 leading-relaxed">{value.description}</p>
                </CardContent>
              </Card>
            </motion.div>
          ))}
        </div>
      </div>
    </div>
  );
}