import React from 'react';
import { Navigate, useLocation } from 'react-router-dom';
import { useAuth } from '../hooks/useAuth';
import LoadingScreen from './LoadingScreen';

const ProtectedRoute = ({ children, requiredTier = null, requiredRole = null }) => {
  const { isAuthenticated, user, loading } = useAuth();
  const location = useLocation();

  // Show loading screen while checking authentication
  if (loading) {
    return <LoadingScreen />;
  }

  // Redirect to login if not authenticated
  if (!isAuthenticated || !user) {
    return (
      <Navigate 
        to="/login" 
        state={{ from: location.pathname }} 
        replace 
      />
    );
  }

  // Check subscription tier if required
  if (requiredTier) {
    const tierHierarchy = {
      'free': 0,
      'pro': 1,
      'business': 2,
      'enterprise': 3
    };

    const userTierLevel = tierHierarchy[user.subscriptionTier] || 0;
    const requiredLevel = tierHierarchy[requiredTier] || 0;

    if (userTierLevel < requiredLevel) {
      return (
        <Navigate 
          to="/pricing" 
          state={{ 
            from: location.pathname,
            requiredTier,
            message: `This feature requires a ${requiredTier} subscription or higher.`
          }} 
          replace 
        />
      );
    }
  }

  // Check user role if required
  if (requiredRole) {
    const roleHierarchy = {
      'user': 0,
      'moderator': 1,
      'admin': 2
    };

    const userRoleLevel = roleHierarchy[user.role] || 0;
    const requiredRoleLevel = roleHierarchy[requiredRole] || 0;

    if (userRoleLevel < requiredRoleLevel) {
      return (
        <Navigate 
          to="/dashboard" 
          state={{ 
            from: location.pathname,
            message: `Access denied. This feature requires ${requiredRole} privileges.`
          }} 
          replace 
        />
      );
    }
  }

  // Check if account is active
  if (!user.isActive) {
    return (
      <Navigate 
        to="/login" 
        state={{ 
          from: location.pathname,
          message: 'Your account has been deactivated. Please contact support.'
        }} 
        replace 
      />
    );
  }

  // Check subscription expiry for paid features
  const checkSubscriptionExpiry = () => {
    if (user.subscriptionTier !== 'free' && user.subscriptionExpiresAt) {
      const expiryDate = new Date(user.subscriptionExpiresAt);
      const now = new Date();
      
      if (expiryDate <= now) {
        return false; // Subscription expired
      }
    }
    return true;
  };

  if (!checkSubscriptionExpiry()) {
    return (
      <Navigate 
        to="/pricing" 
        state={{ 
          from: location.pathname,
          message: 'Your subscription has expired. Please renew to continue using premium features.'
        }} 
        replace 
      />
    );
  }

  // All checks passed, render the protected component
  return children;
};

export default ProtectedRoute;
