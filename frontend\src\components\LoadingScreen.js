import React from 'react';
import { motion } from 'framer-motion';
import { Zap, Globe, Shield } from 'lucide-react';

const LoadingScreen = ({ message = 'Loading ProxyForge...', fullScreen = true }) => {
  const containerClass = fullScreen 
    ? 'fixed inset-0 bg-black flex items-center justify-center z-50'
    : 'flex items-center justify-center p-8';

  return (
    <div className={containerClass}>
      <div className="text-center">
        {/* Animated Logo */}
        <motion.div
          initial={{ scale: 0, rotate: -180 }}
          animate={{ scale: 1, rotate: 0 }}
          transition={{ duration: 0.8, ease: "easeOut" }}
          className="mb-8"
        >
          <div className="relative">
            {/* Main logo container */}
            <div className="w-24 h-24 bg-[#FF0066] border-4 border-white mx-auto flex items-center justify-center transform rotate-12 shadow-[8px_8px_0px_0px_#000000]">
              <Zap className="w-12 h-12 text-white" />
            </div>
            
            {/* Orbiting icons */}
            <motion.div
              animate={{ rotate: 360 }}
              transition={{ duration: 3, repeat: Infinity, ease: "linear" }}
              className="absolute inset-0"
            >
              <div className="relative w-24 h-24">
                <div className="absolute -top-2 left-1/2 transform -translate-x-1/2">
                  <div className="w-6 h-6 bg-[#00FF66] border-2 border-white rounded-full flex items-center justify-center">
                    <Globe className="w-3 h-3 text-black" />
                  </div>
                </div>
                <div className="absolute -bottom-2 left-1/2 transform -translate-x-1/2">
                  <div className="w-6 h-6 bg-[#0066FF] border-2 border-white rounded-full flex items-center justify-center">
                    <Shield className="w-3 h-3 text-white" />
                  </div>
                </div>
              </div>
            </motion.div>
          </div>
        </motion.div>

        {/* Brand Name */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.3, duration: 0.6 }}
          className="mb-6"
        >
          <h1 className="text-4xl font-black text-white mb-2 transform -rotate-1">
            PROXYFORGE
          </h1>
          <p className="text-lg font-bold text-[#00FF66] transform rotate-1">
            BRUTAL IP SWITCHER
          </p>
        </motion.div>

        {/* Loading Message */}
        <motion.div
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          transition={{ delay: 0.6, duration: 0.4 }}
          className="mb-8"
        >
          <p className="text-white font-bold text-lg">{message}</p>
        </motion.div>

        {/* Loading Animation */}
        <motion.div
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          transition={{ delay: 0.8, duration: 0.4 }}
          className="flex justify-center space-x-2"
        >
          {[0, 1, 2].map((index) => (
            <motion.div
              key={index}
              animate={{
                scale: [1, 1.2, 1],
                backgroundColor: ['#FF0066', '#00FF66', '#0066FF', '#FF0066']
              }}
              transition={{
                duration: 1.5,
                repeat: Infinity,
                delay: index * 0.2,
                ease: "easeInOut"
              }}
              className="w-4 h-4 border-2 border-white shadow-[2px_2px_0px_0px_#000000]"
            />
          ))}
        </motion.div>

        {/* Progress Bar */}
        <motion.div
          initial={{ opacity: 0, width: 0 }}
          animate={{ opacity: 1, width: '100%' }}
          transition={{ delay: 1, duration: 2 }}
          className="mt-8 mx-auto max-w-xs"
        >
          <div className="h-2 bg-white border-2 border-black shadow-[2px_2px_0px_0px_#000000]">
            <motion.div
              animate={{ width: ['0%', '100%'] }}
              transition={{ duration: 2, repeat: Infinity, ease: "easeInOut" }}
              className="h-full bg-gradient-to-r from-[#FF0066] via-[#00FF66] to-[#0066FF]"
            />
          </div>
        </motion.div>

        {/* Loading Tips */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 1.2, duration: 0.6 }}
          className="mt-8 max-w-md mx-auto"
        >
          <div className="bg-[#0066FF] border-4 border-white p-4 shadow-[4px_4px_0px_0px_#000000] transform -rotate-1">
            <p className="text-white font-bold text-sm">
              💡 TIP: Use different locations to bypass geo-restrictions!
            </p>
          </div>
        </motion.div>
      </div>

      {/* Background Animation */}
      <div className="absolute inset-0 overflow-hidden pointer-events-none">
        {[...Array(20)].map((_, i) => (
          <motion.div
            key={i}
            initial={{ 
              x: Math.random() * window.innerWidth,
              y: Math.random() * window.innerHeight,
              opacity: 0
            }}
            animate={{ 
              x: Math.random() * window.innerWidth,
              y: Math.random() * window.innerHeight,
              opacity: [0, 0.3, 0]
            }}
            transition={{
              duration: Math.random() * 10 + 5,
              repeat: Infinity,
              delay: Math.random() * 5
            }}
            className="absolute w-2 h-2 bg-[#00FF66] border border-white"
            style={{
              transform: `rotate(${Math.random() * 360}deg)`
            }}
          />
        ))}
      </div>
    </div>
  );
};

export default LoadingScreen;
