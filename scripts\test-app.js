#!/usr/bin/env node

/**
 * ProxyForge Application Test Script
 * Tests the backend API endpoints and proxy functionality
 */

const axios = require('axios');
const colors = require('colors');

// Configuration
const API_BASE_URL = process.env.API_URL || 'http://localhost:5000/api';
const PROXY_URL = process.env.PROXY_URL || 'http://localhost:8080';
const TEST_USER = {
  email: '<EMAIL>',
  password: 'test123',
  firstName: 'Test',
  lastName: 'User'
};

let authToken = null;

// Test results tracking
const results = {
  passed: 0,
  failed: 0,
  tests: []
};

// Helper functions
function log(message, type = 'info') {
  const timestamp = new Date().toISOString();
  const prefix = `[${timestamp}]`;
  
  switch (type) {
    case 'success':
      console.log(`${prefix} ✅ ${message}`.green);
      break;
    case 'error':
      console.log(`${prefix} ❌ ${message}`.red);
      break;
    case 'warning':
      console.log(`${prefix} ⚠️  ${message}`.yellow);
      break;
    case 'info':
    default:
      console.log(`${prefix} ℹ️  ${message}`.blue);
      break;
  }
}

function recordTest(name, passed, error = null) {
  results.tests.push({ name, passed, error });
  if (passed) {
    results.passed++;
    log(`${name} - PASSED`, 'success');
  } else {
    results.failed++;
    log(`${name} - FAILED: ${error}`, 'error');
  }
}

async function makeRequest(method, endpoint, data = null, headers = {}) {
  try {
    const config = {
      method,
      url: `${API_BASE_URL}${endpoint}`,
      headers: {
        'Content-Type': 'application/json',
        ...headers
      }
    };

    if (data) {
      config.data = data;
    }

    if (authToken) {
      config.headers.Authorization = `Bearer ${authToken}`;
    }

    const response = await axios(config);
    return { success: true, data: response.data, status: response.status };
  } catch (error) {
    return {
      success: false,
      error: error.response?.data?.message || error.message,
      status: error.response?.status || 500
    };
  }
}

// Test functions
async function testHealthCheck() {
  log('Testing health check endpoint...');
  
  try {
    const response = await axios.get(`${API_BASE_URL.replace('/api', '')}/health`);
    
    if (response.status === 200 && response.data.status === 'OK') {
      recordTest('Health Check', true);
      return true;
    } else {
      recordTest('Health Check', false, 'Invalid health check response');
      return false;
    }
  } catch (error) {
    recordTest('Health Check', false, error.message);
    return false;
  }
}

async function testUserRegistration() {
  log('Testing user registration...');
  
  const result = await makeRequest('POST', '/auth/register', TEST_USER);
  
  if (result.success && result.data.accessToken) {
    authToken = result.data.accessToken;
    recordTest('User Registration', true);
    return true;
  } else {
    recordTest('User Registration', false, result.error);
    return false;
  }
}

async function testUserLogin() {
  log('Testing user login...');
  
  const result = await makeRequest('POST', '/auth/login', {
    email: TEST_USER.email,
    password: TEST_USER.password
  });
  
  if (result.success && result.data.accessToken) {
    authToken = result.data.accessToken;
    recordTest('User Login', true);
    return true;
  } else {
    recordTest('User Login', false, result.error);
    return false;
  }
}

async function testUserProfile() {
  log('Testing user profile retrieval...');
  
  const result = await makeRequest('GET', '/user/profile');
  
  if (result.success && result.data.user) {
    recordTest('User Profile', true);
    return true;
  } else {
    recordTest('User Profile', false, result.error);
    return false;
  }
}

async function testProxyServers() {
  log('Testing proxy servers endpoint...');
  
  const result = await makeRequest('GET', '/proxy/servers');
  
  if (result.success && Array.isArray(result.data.servers)) {
    recordTest('Proxy Servers List', true);
    return true;
  } else {
    recordTest('Proxy Servers List', false, result.error);
    return false;
  }
}

async function testProxyConnection() {
  log('Testing proxy connection...');
  
  const result = await makeRequest('POST', '/proxy/connect', {
    country: 'US',
    proxyType: 'http'
  });
  
  if (result.success && result.data.connection) {
    recordTest('Proxy Connection', true);
    return true;
  } else {
    recordTest('Proxy Connection', false, result.error);
    return false;
  }
}

async function testProxyStats() {
  log('Testing proxy statistics...');
  
  const result = await makeRequest('GET', '/proxy/stats');
  
  if (result.success && typeof result.data.totalConnections === 'number') {
    recordTest('Proxy Statistics', true);
    return true;
  } else {
    recordTest('Proxy Statistics', false, result.error);
    return false;
  }
}

async function testRateLimiting() {
  log('Testing rate limiting...');
  
  try {
    // Make multiple rapid requests to trigger rate limiting
    const promises = Array(10).fill().map(() => 
      makeRequest('GET', '/proxy/servers')
    );
    
    const results = await Promise.all(promises);
    const rateLimited = results.some(r => r.status === 429);
    
    if (rateLimited) {
      recordTest('Rate Limiting', true);
      return true;
    } else {
      recordTest('Rate Limiting', false, 'Rate limiting not triggered');
      return false;
    }
  } catch (error) {
    recordTest('Rate Limiting', false, error.message);
    return false;
  }
}

async function testProxyServer() {
  log('Testing proxy server functionality...');
  
  try {
    // Test if proxy server is running
    const response = await axios.get(PROXY_URL, {
      timeout: 5000,
      validateStatus: () => true // Accept any status code
    });
    
    // Proxy server should return some response (even if it's an error)
    if (response.status) {
      recordTest('Proxy Server Running', true);
      return true;
    } else {
      recordTest('Proxy Server Running', false, 'No response from proxy server');
      return false;
    }
  } catch (error) {
    if (error.code === 'ECONNREFUSED') {
      recordTest('Proxy Server Running', false, 'Proxy server not running');
    } else {
      recordTest('Proxy Server Running', false, error.message);
    }
    return false;
  }
}

async function testInvalidEndpoints() {
  log('Testing invalid endpoints...');
  
  const result = await makeRequest('GET', '/invalid/endpoint');
  
  if (result.status === 404) {
    recordTest('404 Handling', true);
    return true;
  } else {
    recordTest('404 Handling', false, 'Should return 404 for invalid endpoints');
    return false;
  }
}

async function testUnauthorizedAccess() {
  log('Testing unauthorized access...');
  
  // Temporarily remove auth token
  const tempToken = authToken;
  authToken = null;
  
  const result = await makeRequest('GET', '/user/profile');
  
  // Restore auth token
  authToken = tempToken;
  
  if (result.status === 401) {
    recordTest('Unauthorized Access', true);
    return true;
  } else {
    recordTest('Unauthorized Access', false, 'Should return 401 for unauthorized access');
    return false;
  }
}

// Main test runner
async function runTests() {
  console.log('🚀 Starting ProxyForge Application Tests\n'.cyan.bold);
  
  const tests = [
    testHealthCheck,
    testUserRegistration,
    testUserLogin,
    testUserProfile,
    testProxyServers,
    testProxyConnection,
    testProxyStats,
    testProxyServer,
    testRateLimiting,
    testInvalidEndpoints,
    testUnauthorizedAccess
  ];
  
  for (const test of tests) {
    try {
      await test();
    } catch (error) {
      log(`Test failed with unexpected error: ${error.message}`, 'error');
    }
    
    // Small delay between tests
    await new Promise(resolve => setTimeout(resolve, 500));
  }
  
  // Print summary
  console.log('\n📊 Test Results Summary'.cyan.bold);
  console.log('='.repeat(50));
  console.log(`Total Tests: ${results.tests.length}`);
  console.log(`Passed: ${results.passed}`.green);
  console.log(`Failed: ${results.failed}`.red);
  console.log(`Success Rate: ${((results.passed / results.tests.length) * 100).toFixed(1)}%`);
  
  if (results.failed > 0) {
    console.log('\n❌ Failed Tests:'.red.bold);
    results.tests
      .filter(test => !test.passed)
      .forEach(test => {
        console.log(`  - ${test.name}: ${test.error}`.red);
      });
  }
  
  console.log('\n✅ Test run completed!'.green.bold);
  
  // Exit with appropriate code
  process.exit(results.failed > 0 ? 1 : 0);
}

// Handle command line arguments
if (process.argv.includes('--help') || process.argv.includes('-h')) {
  console.log(`
ProxyForge Application Test Script

Usage: node test-app.js [options]

Options:
  --help, -h     Show this help message
  --api-url      Set API base URL (default: http://localhost:5000/api)
  --proxy-url    Set proxy server URL (default: http://localhost:8080)

Environment Variables:
  API_URL        API base URL
  PROXY_URL      Proxy server URL

Examples:
  node test-app.js
  node test-app.js --api-url http://localhost:5000/api
  API_URL=http://production-api.com/api node test-app.js
  `.trim());
  process.exit(0);
}

// Parse command line arguments
const apiUrlIndex = process.argv.indexOf('--api-url');
if (apiUrlIndex !== -1 && process.argv[apiUrlIndex + 1]) {
  API_BASE_URL = process.argv[apiUrlIndex + 1];
}

const proxyUrlIndex = process.argv.indexOf('--proxy-url');
if (proxyUrlIndex !== -1 && process.argv[proxyUrlIndex + 1]) {
  PROXY_URL = process.argv[proxyUrlIndex + 1];
}

// Run tests
runTests().catch(error => {
  log(`Test runner failed: ${error.message}`, 'error');
  process.exit(1);
});
