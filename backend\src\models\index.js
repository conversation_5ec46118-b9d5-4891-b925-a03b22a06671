const { Sequelize, DataTypes } = require('sequelize');
const path = require('path');
const logger = require('../utils/logger');

// Database configuration
const config = {
  development: {
    dialect: 'sqlite',
    storage: path.join(__dirname, '../../database/development.sqlite'),
    logging: (msg) => logger.logDatabase(msg, 0)
  },
  test: {
    dialect: 'sqlite',
    storage: ':memory:',
    logging: false
  },
  production: {
    dialect: 'postgres',
    url: process.env.DATABASE_URL,
    dialectOptions: {
      ssl: process.env.DATABASE_SSL === 'true' ? {
        require: true,
        rejectUnauthorized: false
      } : false
    },
    logging: false
  }
};

const env = process.env.NODE_ENV || 'development';
const dbConfig = config[env];

// Initialize Sequelize
const sequelize = new Sequelize(dbConfig.url || dbConfig.storage, dbConfig);

// Test database connection
const testConnection = async () => {
  try {
    await sequelize.authenticate();
    logger.info(`Database connection established (${env})`);
  } catch (error) {
    logger.error('Unable to connect to database:', error);
    throw error;
  }
};

// User Model
const User = sequelize.define('User', {
  id: {
    type: DataTypes.UUID,
    defaultValue: DataTypes.UUIDV4,
    primaryKey: true
  },
  email: {
    type: DataTypes.STRING,
    allowNull: false,
    unique: true,
    validate: {
      isEmail: true
    }
  },
  password: {
    type: DataTypes.STRING,
    allowNull: false
  },
  firstName: {
    type: DataTypes.STRING,
    allowNull: true
  },
  lastName: {
    type: DataTypes.STRING,
    allowNull: true
  },
  subscriptionTier: {
    type: DataTypes.ENUM('free', 'pro', 'business', 'enterprise'),
    defaultValue: 'free'
  },
  role: {
    type: DataTypes.ENUM('user', 'admin', 'moderator'),
    defaultValue: 'user'
  },
  isActive: {
    type: DataTypes.BOOLEAN,
    defaultValue: true
  },
  isEmailVerified: {
    type: DataTypes.BOOLEAN,
    defaultValue: false
  },
  lastLoginAt: {
    type: DataTypes.DATE,
    allowNull: true
  },
  subscriptionExpiresAt: {
    type: DataTypes.DATE,
    allowNull: true
  },
  apiKey: {
    type: DataTypes.STRING,
    allowNull: true,
    unique: true
  },
  preferences: {
    type: DataTypes.JSON,
    defaultValue: {}
  }
}, {
  tableName: 'users',
  timestamps: true,
  indexes: [
    { fields: ['email'] },
    { fields: ['subscriptionTier'] },
    { fields: ['isActive'] }
  ]
});

// ProxyServer Model
const ProxyServer = sequelize.define('ProxyServer', {
  id: {
    type: DataTypes.UUID,
    defaultValue: DataTypes.UUIDV4,
    primaryKey: true
  },
  host: {
    type: DataTypes.STRING,
    allowNull: false
  },
  port: {
    type: DataTypes.INTEGER,
    allowNull: false
  },
  type: {
    type: DataTypes.ENUM('http', 'https', 'socks4', 'socks5'),
    defaultValue: 'http'
  },
  country: {
    type: DataTypes.STRING,
    allowNull: false
  },
  countryCode: {
    type: DataTypes.STRING(2),
    allowNull: true
  },
  city: {
    type: DataTypes.STRING,
    allowNull: true
  },
  provider: {
    type: DataTypes.STRING,
    allowNull: true
  },
  speed: {
    type: DataTypes.INTEGER,
    defaultValue: 0,
    comment: 'Speed in Mbps'
  },
  reliability: {
    type: DataTypes.FLOAT,
    defaultValue: 0.0,
    validate: {
      min: 0.0,
      max: 1.0
    }
  },
  latency: {
    type: DataTypes.INTEGER,
    defaultValue: 0,
    comment: 'Latency in milliseconds'
  },
  isActive: {
    type: DataTypes.BOOLEAN,
    defaultValue: true
  },
  lastCheckedAt: {
    type: DataTypes.DATE,
    allowNull: true
  },
  metadata: {
    type: DataTypes.JSON,
    defaultValue: {}
  }
}, {
  tableName: 'proxy_servers',
  timestamps: true,
  indexes: [
    { fields: ['country'] },
    { fields: ['type'] },
    { fields: ['isActive'] },
    { fields: ['reliability'] },
    { fields: ['speed'] }
  ]
});

// Connection Model
const Connection = sequelize.define('Connection', {
  id: {
    type: DataTypes.UUID,
    defaultValue: DataTypes.UUIDV4,
    primaryKey: true
  },
  userId: {
    type: DataTypes.UUID,
    allowNull: false,
    references: {
      model: User,
      key: 'id'
    }
  },
  proxyServerId: {
    type: DataTypes.UUID,
    allowNull: true,
    references: {
      model: ProxyServer,
      key: 'id'
    }
  },
  proxyHost: {
    type: DataTypes.STRING,
    allowNull: false
  },
  proxyPort: {
    type: DataTypes.INTEGER,
    allowNull: false
  },
  targetUrl: {
    type: DataTypes.TEXT,
    allowNull: false
  },
  method: {
    type: DataTypes.STRING,
    defaultValue: 'GET'
  },
  statusCode: {
    type: DataTypes.INTEGER,
    allowNull: true
  },
  responseTime: {
    type: DataTypes.INTEGER,
    allowNull: true,
    comment: 'Response time in milliseconds'
  },
  bytesTransferred: {
    type: DataTypes.BIGINT,
    defaultValue: 0
  },
  userAgent: {
    type: DataTypes.TEXT,
    allowNull: true
  },
  clientIp: {
    type: DataTypes.STRING,
    allowNull: true
  },
  errorMessage: {
    type: DataTypes.TEXT,
    allowNull: true
  },
  metadata: {
    type: DataTypes.JSON,
    defaultValue: {}
  }
}, {
  tableName: 'connections',
  timestamps: true,
  indexes: [
    { fields: ['userId'] },
    { fields: ['proxyServerId'] },
    { fields: ['createdAt'] },
    { fields: ['statusCode'] }
  ]
});

// UserSession Model
const UserSession = sequelize.define('UserSession', {
  id: {
    type: DataTypes.UUID,
    defaultValue: DataTypes.UUIDV4,
    primaryKey: true
  },
  userId: {
    type: DataTypes.UUID,
    allowNull: false,
    references: {
      model: User,
      key: 'id'
    }
  },
  sessionToken: {
    type: DataTypes.STRING,
    allowNull: false,
    unique: true
  },
  refreshToken: {
    type: DataTypes.STRING,
    allowNull: true,
    unique: true
  },
  ipAddress: {
    type: DataTypes.STRING,
    allowNull: true
  },
  userAgent: {
    type: DataTypes.TEXT,
    allowNull: true
  },
  isActive: {
    type: DataTypes.BOOLEAN,
    defaultValue: true
  },
  expiresAt: {
    type: DataTypes.DATE,
    allowNull: false
  },
  lastActivityAt: {
    type: DataTypes.DATE,
    defaultValue: DataTypes.NOW
  }
}, {
  tableName: 'user_sessions',
  timestamps: true,
  indexes: [
    { fields: ['userId'] },
    { fields: ['sessionToken'] },
    { fields: ['refreshToken'] },
    { fields: ['isActive'] },
    { fields: ['expiresAt'] }
  ]
});

// Define associations
User.hasMany(Connection, { foreignKey: 'userId', as: 'connections' });
Connection.belongsTo(User, { foreignKey: 'userId', as: 'user' });

ProxyServer.hasMany(Connection, { foreignKey: 'proxyServerId', as: 'connections' });
Connection.belongsTo(ProxyServer, { foreignKey: 'proxyServerId', as: 'proxyServer' });

User.hasMany(UserSession, { foreignKey: 'userId', as: 'sessions' });
UserSession.belongsTo(User, { foreignKey: 'userId', as: 'user' });

// Database initialization
const initializeDatabase = async () => {
  try {
    await testConnection();
    
    // Create database directory for SQLite
    if (env === 'development') {
      const fs = require('fs');
      const dbDir = path.join(__dirname, '../../database');
      if (!fs.existsSync(dbDir)) {
        fs.mkdirSync(dbDir, { recursive: true });
      }
    }
    
    // Sync models
    await sequelize.sync({ alter: env === 'development' });
    logger.info('Database models synchronized');
    
    // Create default admin user in development
    if (env === 'development') {
      await createDefaultUsers();
    }
    
  } catch (error) {
    logger.error('Database initialization failed:', error);
    throw error;
  }
};

// Create default users for development
const createDefaultUsers = async () => {
  try {
    const bcrypt = require('bcryptjs');
    
    const adminExists = await User.findOne({ where: { email: '<EMAIL>' } });
    if (!adminExists) {
      const hashedPassword = await bcrypt.hash('admin123', 10);
      await User.create({
        email: '<EMAIL>',
        password: hashedPassword,
        firstName: 'Admin',
        lastName: 'User',
        role: 'admin',
        subscriptionTier: 'enterprise',
        isEmailVerified: true
      });
      logger.info('Default admin user created');
    }
    
    const testUserExists = await User.findOne({ where: { email: '<EMAIL>' } });
    if (!testUserExists) {
      const hashedPassword = await bcrypt.hash('test123', 10);
      await User.create({
        email: '<EMAIL>',
        password: hashedPassword,
        firstName: 'Test',
        lastName: 'User',
        subscriptionTier: 'pro',
        isEmailVerified: true
      });
      logger.info('Default test user created');
    }
  } catch (error) {
    logger.error('Error creating default users:', error);
  }
};

module.exports = {
  sequelize,
  User,
  ProxyServer,
  Connection,
  UserSession,
  initializeDatabase,
  testConnection
};
