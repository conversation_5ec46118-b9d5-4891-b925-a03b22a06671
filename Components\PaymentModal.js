import React, { useState } from 'react';
import { <PERSON><PERSON>, <PERSON>alog<PERSON>ontent, DialogHeader, DialogTitle } from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Badge } from '@/components/ui/badge';
import { CreditCard, Lock, Shield, CheckCircle } from 'lucide-react';
import { motion } from 'framer-motion';

export default function PaymentModal({ isOpen, onClose, plan, onSuccess }) {
  const [paymentData, setPaymentData] = useState({
    cardNumber: '',
    expiryDate: '',
    cvv: '',
    name: '',
    email: '',
    country: ''
  });
  const [isProcessing, setIsProcessing] = useState(false);
  const [step, setStep] = useState('payment'); // payment, processing, success

  const handleSubmit = async (e) => {
    e.preventDefault();
    setIsProcessing(true);
    setStep('processing');

    // Simulate payment processing
    setTimeout(() => {
      setStep('success');
      setTimeout(() => {
        onSuccess?.();
        onClose();
        setStep('payment');
        setIsProcessing(false);
      }, 3000);
    }, 2000);
  };

  const formatCardNumber = (value) => {
    return value.replace(/\s/g, '').replace(/(.{4})/g, '$1 ').trim();
  };

  const formatExpiry = (value) => {
    return value.replace(/\D/g, '').replace(/(\d{2})(\d)/, '$1/$2').substr(0, 5);
  };

  if (step === 'processing') {
    return (
      <Dialog open={isOpen} onOpenChange={onClose}>
        <DialogContent className="border-4 border-black shadow-[12px_12px_0px_0px_#FF0066] bg-white font-mono max-w-md">
          <div className="text-center py-12">
            <motion.div
              animate={{ rotate: 360 }}
              transition={{ duration: 2, repeat: Infinity, ease: "linear" }}
              className="w-16 h-16 mx-auto mb-6 border-8 border-black border-t-[#FF0066] rounded-full"
            />
            <h3 className="text-2xl font-black mb-4">PROCESSING PAYMENT</h3>
            <p className="font-bold text-gray-600">
              Securing your brutal subscription...
            </p>
          </div>
        </DialogContent>
      </Dialog>
    );
  }

  if (step === 'success') {
    return (
      <Dialog open={isOpen} onOpenChange={onClose}>
        <DialogContent className="border-4 border-black shadow-[12px_12px_0px_0px_#00FF66] bg-white font-mono max-w-md">
          <div className="text-center py-12">
            <motion.div
              initial={{ scale: 0 }}
              animate={{ scale: 1 }}
              transition={{ type: "spring", stiffness: 200, damping: 10 }}
              className="w-20 h-20 mx-auto mb-6 bg-[#00FF66] border-4 border-black rounded-full flex items-center justify-center"
            >
              <CheckCircle className="w-12 h-12 text-black" />
            </motion.div>
            <h3 className="text-2xl font-black mb-4">PAYMENT SUCCESS!</h3>
            <p className="font-bold text-gray-600 mb-4">
              Welcome to GeoShift Pro {plan?.name}!
            </p>
            <Badge className="bg-[#FF0066] text-white border-4 border-black font-black px-4 py-2">
              ACCOUNT ACTIVATED
            </Badge>
          </div>
        </DialogContent>
      </Dialog>
    );
  }

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="border-4 border-black shadow-[12px_12px_0px_0px_#FF0066] bg-white font-mono max-w-2xl">
        <DialogHeader className="border-b-4 border-black bg-[#FF0066] text-white p-6 -m-6 mb-6">
          <DialogTitle className="text-3xl font-black flex items-center gap-4">
            <CreditCard className="w-8 h-8" />
            BRUTAL CHECKOUT
          </DialogTitle>
        </DialogHeader>

        <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
          {/* Order Summary */}
          <div>
            <h3 className="text-2xl font-black mb-4">ORDER SUMMARY</h3>
            <div className="p-6 border-4 border-black bg-black text-white">
              <div className="flex justify-between items-start mb-4">
                <div>
                  <h4 className="text-xl font-black text-[#00FF66]">{plan?.name}</h4>
                  <p className="font-bold">Monthly Subscription</p>
                </div>
                <Badge className="bg-[#FFFF00] text-black border-2 border-white font-black">
                  POPULAR
                </Badge>
              </div>
              
              <div className="space-y-2 mb-6">
                <div className="flex justify-between font-bold">
                  <span>Subtotal:</span>
                  <span>${plan?.price || '9.99'}</span>
                </div>
                <div className="flex justify-between font-bold">
                  <span>Tax:</span>
                  <span>$0.00</span>
                </div>
                <div className="border-t-2 border-white pt-2 flex justify-between text-xl font-black">
                  <span>Total:</span>
                  <span className="text-[#00FF66]">${plan?.price || '9.99'}</span>
                </div>
              </div>

              <div className="flex items-center gap-2 text-sm">
                <Shield className="w-4 h-4 text-[#00FF66]" />
                <span className="font-bold">30-day money-back guarantee</span>
              </div>
            </div>
          </div>

          {/* Payment Form */}
          <div>
            <h3 className="text-2xl font-black mb-4">PAYMENT DETAILS</h3>
            <form onSubmit={handleSubmit} className="space-y-6">
              <div>
                <Label className="text-lg font-black">EMAIL ADDRESS</Label>
                <Input
                  type="email"
                  required
                  value={paymentData.email}
                  onChange={(e) => setPaymentData({...paymentData, email: e.target.value})}
                  className="border-4 border-black text-lg font-bold h-12 mt-2"
                  placeholder="<EMAIL>"
                />
              </div>

              <div>
                <Label className="text-lg font-black">CARDHOLDER NAME</Label>
                <Input
                  required
                  value={paymentData.name}
                  onChange={(e) => setPaymentData({...paymentData, name: e.target.value})}
                  className="border-4 border-black text-lg font-bold h-12 mt-2"
                  placeholder="JOHN BRUTAL"
                />
              </div>

              <div>
                <Label className="text-lg font-black">CARD NUMBER</Label>
                <Input
                  required
                  value={paymentData.cardNumber}
                  onChange={(e) => setPaymentData({...paymentData, cardNumber: formatCardNumber(e.target.value)})}
                  className="border-4 border-black text-lg font-bold h-12 mt-2"
                  placeholder="1234 5678 9012 3456"
                  maxLength={19}
                />
              </div>

              <div className="grid grid-cols-2 gap-4">
                <div>
                  <Label className="text-lg font-black">EXPIRY DATE</Label>
                  <Input
                    required
                    value={paymentData.expiryDate}
                    onChange={(e) => setPaymentData({...paymentData, expiryDate: formatExpiry(e.target.value)})}
                    className="border-4 border-black text-lg font-bold h-12 mt-2"
                    placeholder="MM/YY"
                    maxLength={5}
                  />
                </div>
                <div>
                  <Label className="text-lg font-black">CVV</Label>
                  <Input
                    required
                    value={paymentData.cvv}
                    onChange={(e) => setPaymentData({...paymentData, cvv: e.target.value.replace(/\D/g, '')})}
                    className="border-4 border-black text-lg font-bold h-12 mt-2"
                    placeholder="123"
                    maxLength={4}
                  />
                </div>
              </div>

              <div>
                <Label className="text-lg font-black">COUNTRY</Label>
                <Select 
                  value={paymentData.country} 
                  onValueChange={(value) => setPaymentData({...paymentData, country: value})}
                >
                  <SelectTrigger className="border-4 border-black text-lg font-bold h-12 mt-2">
                    <SelectValue placeholder="SELECT COUNTRY" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="US">United States</SelectItem>
                    <SelectItem value="CA">Canada</SelectItem>
                    <SelectItem value="GB">United Kingdom</SelectItem>
                    <SelectItem value="DE">Germany</SelectItem>
                    <SelectItem value="FR">France</SelectItem>
                    <SelectItem value="AU">Australia</SelectItem>
                  </SelectContent>
                </Select>
              </div>

              <div className="p-4 border-4 border-black bg-[#00FF66] text-black">
                <div className="flex items-center gap-2 mb-2">
                  <Lock className="w-5 h-5" />
                  <span className="font-black">SECURE PAYMENT</span>
                </div>
                <p className="text-sm font-bold">
                  Your payment information is encrypted and secure. We never store your card details.
                </p>
              </div>

              <Button
                type="submit"
                disabled={isProcessing}
                className="w-full text-xl font-black py-6 border-4 border-black shadow-[6px_6px_0px_0px_#000000] bg-[#FF0066] text-white hover:bg-[#FF0066]/90 transform hover:rotate-1 transition-all"
              >
                {isProcessing ? (
                  'PROCESSING...'
                ) : (
                  <>
                    <Shield className="w-6 h-6 mr-2" />
                    COMPLETE PAYMENT
                  </>
                )}
              </Button>
            </form>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  );
}