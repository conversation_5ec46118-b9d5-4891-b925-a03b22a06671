const { contextBridge, ipc<PERSON><PERSON><PERSON> } = require('electron');

// Expose protected methods that allow the renderer process to use
// the ipcRenderer without exposing the entire object
contextBridge.exposeInMainWorld('electronAPI', {
  // App information
  getAppVersion: () => ipcRenderer.invoke('get-app-version'),
  getAppPath: (name) => ipcRenderer.invoke('get-app-path', name),

  // Dialog methods
  showMessageBox: (options) => ipcRenderer.invoke('show-message-box', options),
  showSaveDialog: (options) => ipcRenderer.invoke('show-save-dialog', options),
  showOpenDialog: (options) => ipcRenderer.invoke('show-open-dialog', options),

  // Store methods
  storeGet: (key) => ipcRenderer.invoke('store-get', key),
  storeSet: (key, value) => ipcRenderer.invoke('store-set', key, value),

  // Notification
  notify: (options) => ipc<PERSON><PERSON><PERSON>.invoke('notify', options),

  // Event listeners
  onNavigateTo: (callback) => {
    ipcRenderer.on('navigate-to', (event, path) => callback(path));
  },
  
  onNewConnection: (callback) => {
    ipcRenderer.on('new-connection', () => callback());
  },
  
  onQuickConnect: (callback) => {
    ipcRenderer.on('quick-connect', (event, options) => callback(options));
  },
  
  onDisconnect: (callback) => {
    ipcRenderer.on('disconnect', () => callback());
  },
  
  onSpeedTest: (callback) => {
    ipcRenderer.on('speed-test', () => callback());
  },

  // Remove listeners
  removeAllListeners: (channel) => {
    ipcRenderer.removeAllListeners(channel);
  },

  // Platform information
  platform: process.platform,
  isElectron: true,
  
  // Environment
  isDev: process.env.NODE_ENV === 'development'
});

// Expose a limited set of Node.js APIs
contextBridge.exposeInMainWorld('nodeAPI', {
  platform: process.platform,
  arch: process.arch,
  versions: process.versions
});

// Security: Remove the eval function
delete window.eval;

// Security: Prevent new Function constructor
window.Function = function() {
  throw new Error('Function constructor is disabled for security reasons');
};

// Log that preload script has loaded
console.log('ProxyForge preload script loaded successfully');
