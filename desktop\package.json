{"name": "proxyforge-desktop", "version": "1.0.0", "description": "ProxyForge Desktop Application", "main": "main.js", "homepage": "./", "scripts": {"start": "electron .", "dev": "concurrently \"npm run start:backend\" \"npm run start:frontend\" \"wait-on http://localhost:3000 && electron .\"", "start:backend": "cd ../backend && npm run dev", "start:frontend": "cd ../frontend && npm start", "build": "npm run build:frontend && npm run build:backend", "build:frontend": "cd ../frontend && npm run build", "build:backend": "cd ../backend && npm run build", "pack": "electron-builder --dir", "dist": "electron-builder", "dist:win": "electron-builder --win", "dist:mac": "electron-builder --mac", "dist:linux": "electron-builder --linux", "postinstall": "electron-builder install-app-deps", "clean": "<PERSON><PERSON><PERSON> dist build"}, "keywords": ["proxy", "vpn", "electron", "desktop", "privacy"], "author": "HectorTa1989", "license": "MIT", "devDependencies": {"electron": "^27.1.3", "electron-builder": "^24.8.1", "concurrently": "^8.2.2", "wait-on": "^7.2.0", "rimraf": "^5.0.5"}, "dependencies": {"electron-updater": "^6.1.7", "electron-store": "^8.1.0", "electron-log": "^5.0.1", "node-notifier": "^10.0.1"}, "build": {"appId": "com.proxyforge.desktop", "productName": "ProxyForge", "directories": {"output": "dist", "buildResources": "build"}, "files": ["main.js", "preload.js", "package.json", "../frontend/build/**/*", "../backend/dist/**/*", "../backend/package.json", "../backend/node_modules/**/*"], "extraResources": [{"from": "../backend/database", "to": "database", "filter": ["**/*"]}], "win": {"target": [{"target": "nsis", "arch": ["x64", "ia32"]}, {"target": "portable", "arch": ["x64"]}], "icon": "build/icon.ico", "publisherName": "ProxyForge", "verifyUpdateCodeSignature": false}, "mac": {"target": [{"target": "dmg", "arch": ["x64", "arm64"]}, {"target": "zip", "arch": ["x64", "arm64"]}], "icon": "build/icon.icns", "category": "public.app-category.utilities", "hardenedRuntime": true, "entitlements": "build/entitlements.mac.plist", "entitlementsInherit": "build/entitlements.mac.plist"}, "linux": {"target": [{"target": "AppImage", "arch": ["x64"]}, {"target": "deb", "arch": ["x64"]}, {"target": "rpm", "arch": ["x64"]}], "icon": "build/icon.png", "category": "Network"}, "nsis": {"oneClick": false, "allowToChangeInstallationDirectory": true, "createDesktopShortcut": true, "createStartMenuShortcut": true, "shortcutName": "ProxyForge"}, "publish": {"provider": "github", "owner": "HectorTa1989", "repo": "proxyforge"}}, "repository": {"type": "git", "url": "https://github.com/HectorTa1989/proxyforge.git"}}