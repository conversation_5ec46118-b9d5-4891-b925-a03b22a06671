version: '3.8'

services:
  # PostgreSQL Database
  postgres:
    image: postgres:15-alpine
    container_name: proxyforge-postgres
    environment:
      POSTGRES_DB: proxyforge
      POSTGRES_USER: proxyforge
      POSTGRES_PASSWORD: proxyforge_password_change_in_production
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./backend/database/init.sql:/docker-entrypoint-initdb.d/init.sql
    ports:
      - "5432:5432"
    networks:
      - proxyforge-network
    restart: unless-stopped
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U proxyforge"]
      interval: 30s
      timeout: 10s
      retries: 3

  # Redis Cache
  redis:
    image: redis:7-alpine
    container_name: proxyforge-redis
    command: redis-server --appendonly yes --requirepass redis_password_change_in_production
    volumes:
      - redis_data:/data
    ports:
      - "6379:6379"
    networks:
      - proxyforge-network
    restart: unless-stopped
    healthcheck:
      test: ["<PERSON><PERSON>", "redis-cli", "--raw", "incr", "ping"]
      interval: 30s
      timeout: 10s
      retries: 3

  # Backend API Server
  backend:
    build:
      context: ./backend
      dockerfile: Dockerfile
    container_name: proxyforge-backend
    environment:
      NODE_ENV: production
      PORT: 5000
      DATABASE_URL: ******************************************************************************/proxyforge
      REDIS_URL: redis://:redis_password_change_in_production@redis:6379
      JWT_SECRET: your_super_secret_jwt_key_change_in_production
      FRONTEND_URL: http://localhost:3000
      PROXY_PORT: 8080
    ports:
      - "5000:5000"
      - "8080:8080"
    depends_on:
      postgres:
        condition: service_healthy
      redis:
        condition: service_healthy
    networks:
      - proxyforge-network
    restart: unless-stopped
    volumes:
      - ./backend/logs:/app/logs
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:5000/health"]
      interval: 30s
      timeout: 10s
      retries: 3

  # Frontend React App (Development)
  frontend:
    build:
      context: ./frontend
      dockerfile: Dockerfile.dev
    container_name: proxyforge-frontend
    environment:
      REACT_APP_API_URL: http://localhost:5000/api
      REACT_APP_SOCKET_URL: http://localhost:5000
      CHOKIDAR_USEPOLLING: true
    ports:
      - "3000:3000"
    depends_on:
      - backend
    networks:
      - proxyforge-network
    volumes:
      - ./frontend/src:/app/src
      - ./frontend/public:/app/public
      - /app/node_modules
    restart: unless-stopped

  # Nginx Reverse Proxy (Production)
  nginx:
    image: nginx:alpine
    container_name: proxyforge-nginx
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./nginx/nginx.conf:/etc/nginx/nginx.conf
      - ./nginx/ssl:/etc/nginx/ssl
      - ./frontend/build:/usr/share/nginx/html
    depends_on:
      - backend
    networks:
      - proxyforge-network
    restart: unless-stopped
    profiles:
      - production

  # Monitoring with Prometheus
  prometheus:
    image: prom/prometheus:latest
    container_name: proxyforge-prometheus
    ports:
      - "9090:9090"
    volumes:
      - ./monitoring/prometheus.yml:/etc/prometheus/prometheus.yml
      - prometheus_data:/prometheus
    command:
      - '--config.file=/etc/prometheus/prometheus.yml'
      - '--storage.tsdb.path=/prometheus'
      - '--web.console.libraries=/etc/prometheus/console_libraries'
      - '--web.console.templates=/etc/prometheus/consoles'
      - '--storage.tsdb.retention.time=200h'
      - '--web.enable-lifecycle'
    networks:
      - proxyforge-network
    restart: unless-stopped
    profiles:
      - monitoring

  # Grafana Dashboard
  grafana:
    image: grafana/grafana:latest
    container_name: proxyforge-grafana
    ports:
      - "3001:3000"
    environment:
      GF_SECURITY_ADMIN_PASSWORD: admin_password_change_in_production
    volumes:
      - grafana_data:/var/lib/grafana
      - ./monitoring/grafana/dashboards:/etc/grafana/provisioning/dashboards
      - ./monitoring/grafana/datasources:/etc/grafana/provisioning/datasources
    depends_on:
      - prometheus
    networks:
      - proxyforge-network
    restart: unless-stopped
    profiles:
      - monitoring

  # Log aggregation with ELK Stack
  elasticsearch:
    image: docker.elastic.co/elasticsearch/elasticsearch:8.11.0
    container_name: proxyforge-elasticsearch
    environment:
      - discovery.type=single-node
      - xpack.security.enabled=false
      - "ES_JAVA_OPTS=-Xms512m -Xmx512m"
    volumes:
      - elasticsearch_data:/usr/share/elasticsearch/data
    ports:
      - "9200:9200"
    networks:
      - proxyforge-network
    restart: unless-stopped
    profiles:
      - logging

  logstash:
    image: docker.elastic.co/logstash/logstash:8.11.0
    container_name: proxyforge-logstash
    volumes:
      - ./logging/logstash.conf:/usr/share/logstash/pipeline/logstash.conf
      - ./backend/logs:/logs
    depends_on:
      - elasticsearch
    networks:
      - proxyforge-network
    restart: unless-stopped
    profiles:
      - logging

  kibana:
    image: docker.elastic.co/kibana/kibana:8.11.0
    container_name: proxyforge-kibana
    ports:
      - "5601:5601"
    environment:
      ELASTICSEARCH_HOSTS: http://elasticsearch:9200
    depends_on:
      - elasticsearch
    networks:
      - proxyforge-network
    restart: unless-stopped
    profiles:
      - logging

# Networks
networks:
  proxyforge-network:
    driver: bridge

# Volumes
volumes:
  postgres_data:
    driver: local
  redis_data:
    driver: local
  prometheus_data:
    driver: local
  grafana_data:
    driver: local
  elasticsearch_data:
    driver: local
