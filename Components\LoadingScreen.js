import React from 'react';
import { motion } from 'framer-motion';
import { Globe, Zap } from 'lucide-react';

export default function LoadingScreen({ message = 'LOADING BRUTAL INTERFACE...' }) {
  return (
    <div className="fixed inset-0 bg-black flex items-center justify-center z-50 font-mono">
      <div className="text-center">
        <motion.div
          animate={{ rotate: 360 }}
          transition={{ duration: 2, repeat: Infinity, ease: "linear" }}
          className="w-32 h-32 mx-auto mb-8 border-8 border-white border-t-[#FF0066] border-r-[#00FF66] border-b-[#0066FF] border-l-[#FFFF00] flex items-center justify-center"
        >
          <Globe className="w-16 h-16 text-white" />
        </motion.div>
        
        <motion.h1
          animate={{ 
            scale: [1, 1.1, 1],
            rotate: [-1, 1, -1]
          }}
          transition={{ 
            duration: 2, 
            repeat: Infinity,
            ease: "easeInOut"
          }}
          className="text-4xl font-black text-white mb-4"
        >
          GEOSHIFT PRO
        </motion.h1>
        
        <motion.p
          animate={{ opacity: [0.5, 1, 0.5] }}
          transition={{ duration: 1.5, repeat: Infinity }}
          className="text-xl font-bold text-[#00FF66]"
        >
          {message}
        </motion.p>
        
        <div className="flex justify-center gap-2 mt-8">
          {[0, 1, 2].map((i) => (
            <motion.div
              key={i}
              animate={{ 
                y: [-10, 10, -10],
                backgroundColor: ['#FF0066', '#00FF66', '#0066FF', '#FFFF00', '#FF0066']
              }}
              transition={{ 
                duration: 1, 
                repeat: Infinity,
                delay: i * 0.2,
                ease: "easeInOut"
              }}
              className="w-4 h-4 border-2 border-white"
            />
          ))}
        </div>
      </div>
    </div>
  );
}