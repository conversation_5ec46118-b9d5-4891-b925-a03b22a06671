import React, { useState, useEffect } from 'react';
import { But<PERSON> } from "@/components/ui/button";
import { <PERSON>, CardContent, Card<PERSON>eader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Globe, Wifi, WifiOff, Shield, Zap, MapPin, Activity, Clock } from 'lucide-react';
import { motion, AnimatePresence } from 'framer-motion';
import { InvokeLLM } from '@/integrations/Core';

export default function Dashboard() {
  const [connectionStatus, setConnectionStatus] = useState('disconnected');
  const [currentLocation, setCurrentLocation] = useState(null);
  const [isConnecting, setIsConnecting] = useState(false);
  const [connectionSpeed, setConnectionSpeed] = useState(0);
  const [uptime, setUptime] = useState(0);
  const [bytesTransferred, setBytesTransferred] = useState(0);

  useEffect(() => {
    // Simulate real-time updates
    const interval = setInterval(() => {
      if (connectionStatus === 'connected') {
        setConnectionSpeed(Math.random() * 100 + 50);
        setUptime(prev => prev + 1);
        setBytesTransferred(prev => prev + Math.random() * 1000);
      }
    }, 1000);

    return () => clearInterval(interval);
  }, [connectionStatus]);

  const handleConnect = async () => {
    if (connectionStatus === 'connected') {
      setConnectionStatus('disconnected');
      setUptime(0);
      setBytesTransferred(0);
      return;
    }

    setIsConnecting(true);
    
    try {
      // Get random location using AI
      const response = await InvokeLLM({
        prompt: "Give me a random country with its flag emoji, capital city, and current local time. Format as JSON.",
        response_json_schema: {
          type: "object",
          properties: {
            country: { type: "string" },
            flag: { type: "string" },
            capital: { type: "string" },
            timezone: { type: "string" },
            ip_address: { type: "string" }
          }
        }
      });

      // Simulate connection delay
      await new Promise(resolve => setTimeout(resolve, 2000));
      
      setCurrentLocation({
        ...response,
        ip_address: `${Math.floor(Math.random() * 255)}.${Math.floor(Math.random() * 255)}.${Math.floor(Math.random() * 255)}.${Math.floor(Math.random() * 255)}`
      });
      setConnectionStatus('connected');
    } catch (error) {
      console.error('Connection failed:', error);
      setConnectionStatus('error');
    }
    
    setIsConnecting(false);
  };

  const getStatusColor = () => {
    switch (connectionStatus) {
      case 'connected': return 'text-[#00FF66] border-[#00FF66] bg-[#00FF66]/10';
      case 'disconnected': return 'text-[#FF0066] border-[#FF0066] bg-[#FF0066]/10';
      case 'error': return 'text-[#FF3300] border-[#FF3300] bg-[#FF3300]/10';
      default: return 'text-gray-500 border-gray-500 bg-gray-500/10';
    }
  };

  return (
    <div className="min-h-screen bg-white p-6 font-mono">
      <div className="max-w-7xl mx-auto">
        {/* Header */}
        <div className="mb-8">
          <h1 className="text-6xl font-black text-black mb-2 transform -rotate-1">
            GEOSHIFT PRO
          </h1>
          <p className="text-xl text-black font-bold transform rotate-0.5">
            BRUTAL IP LOCATION SWITCHER
          </p>
        </div>

        {/* Main Status Card */}
        <Card className="border-4 border-black shadow-[8px_8px_0px_0px_#000000] mb-8 bg-white transform -rotate-0.5">
          <CardHeader className="border-b-4 border-black bg-[#0066FF] text-white">
            <CardTitle className="text-3xl font-black flex items-center gap-4">
              <Globe className="w-8 h-8" />
              CONNECTION STATUS
            </CardTitle>
          </CardHeader>
          <CardContent className="p-8">
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
              {/* Status Display */}
              <div className="space-y-6">
                <div className="flex items-center gap-4">
                  <Badge className={`text-xl px-4 py-2 border-4 font-black ${getStatusColor()}`}>
                    {connectionStatus === 'connected' && <Wifi className="w-6 h-6 mr-2" />}
                    {connectionStatus === 'disconnected' && <WifiOff className="w-6 h-6 mr-2" />}
                    {connectionStatus === 'error' && <WifiOff className="w-6 h-6 mr-2" />}
                    {connectionStatus.toUpperCase()}
                  </Badge>
                </div>

                {currentLocation && connectionStatus === 'connected' && (
                  <motion.div
                    initial={{ opacity: 0, x: -20 }}
                    animate={{ opacity: 1, x: 0 }}
                    className="bg-black text-white p-6 border-4 border-black shadow-[4px_4px_0px_0px_#FF0066] transform rotate-1"
                  >
                    <div className="grid grid-cols-2 gap-4 text-lg font-bold">
                      <div>
                        <p className="text-[#00FF66]">LOCATION:</p>
                        <p className="flex items-center gap-2">
                          <span className="text-3xl">{currentLocation.flag}</span>
                          {currentLocation.country}
                        </p>
                      </div>
                      <div>
                        <p className="text-[#00FF66]">IP ADDRESS:</p>
                        <p className="font-mono">{currentLocation.ip_address}</p>
                      </div>
                      <div>
                        <p className="text-[#00FF66]">CAPITAL:</p>
                        <p>{currentLocation.capital}</p>
                      </div>
                      <div>
                        <p className="text-[#00FF66]">TIMEZONE:</p>
                        <p>{currentLocation.timezone}</p>
                      </div>
                    </div>
                  </motion.div>
                )}
              </div>

              {/* Control Panel */}
              <div className="space-y-6">
                <Button
                  onClick={handleConnect}
                  disabled={isConnecting}
                  className={`w-full h-20 text-2xl font-black border-4 border-black shadow-[6px_6px_0px_0px_#000000] transform rotate-1 hover:rotate-0 transition-transform ${
                    connectionStatus === 'connected' 
                      ? 'bg-[#FF0066] text-white hover:bg-[#FF0066]/90' 
                      : 'bg-[#00FF66] text-black hover:bg-[#00FF66]/90'
                  }`}
                >
                  {isConnecting ? (
                    <motion.div
                      animate={{ rotate: 360 }}
                      transition={{ duration: 1, repeat: Infinity, ease: "linear" }}
                      className="w-8 h-8 border-4 border-current border-t-transparent rounded-full"
                    />
                  ) : connectionStatus === 'connected' ? (
                    'DISCONNECT'
                  ) : (
                    'CONNECT NOW'
                  )}
                </Button>

                {connectionStatus === 'connected' && (
                  <motion.div
                    initial={{ opacity: 0, scale: 0.8 }}
                    animate={{ opacity: 1, scale: 1 }}
                    className="grid grid-cols-2 gap-4"
                  >
                    <Card className="border-4 border-black bg-[#FFFF00] text-black shadow-[4px_4px_0px_0px_#000000]">
                      <CardContent className="p-4 text-center">
                        <Zap className="w-8 h-8 mx-auto mb-2" />
                        <p className="font-black text-xl">{connectionSpeed.toFixed(0)} MBPS</p>
                        <p className="text-sm font-bold">SPEED</p>
                      </CardContent>
                    </Card>
                    <Card className="border-4 border-black bg-[#FF6600] text-white shadow-[4px_4px_0px_0px_#000000]">
                      <CardContent className="p-4 text-center">
                        <Clock className="w-8 h-8 mx-auto mb-2" />
                        <p className="font-black text-xl">{Math.floor(uptime / 60)}:{(uptime % 60).toString().padStart(2, '0')}</p>
                        <p className="text-sm font-bold">UPTIME</p>
                      </CardContent>
                    </Card>
                  </motion.div>
                )}
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Stats Grid */}
        <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
          <Card className="border-4 border-black shadow-[6px_6px_0px_0px_#FF0066] bg-white transform rotate-1">
            <CardHeader className="border-b-4 border-black bg-[#FF0066] text-white">
              <CardTitle className="text-xl font-black flex items-center gap-2">
                <Shield className="w-6 h-6" />
                SECURITY
              </CardTitle>
            </CardHeader>
            <CardContent className="p-4">
              <div className="space-y-2">
                <div className="flex justify-between font-bold">
                  <span>ENCRYPTION:</span>
                  <span className="text-[#00FF66]">AES-256</span>
                </div>
                <div className="flex justify-between font-bold">
                  <span>PROTOCOL:</span>
                  <span className="text-[#0066FF]">OpenVPN</span>
                </div>
                <div className="flex justify-between font-bold">
                  <span>DNS LEAK:</span>
                  <span className="text-[#00FF66]">PROTECTED</span>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card className="border-4 border-black shadow-[6px_6px_0px_0px_#00FF66] bg-white transform -rotate-1">
            <CardHeader className="border-b-4 border-black bg-[#00FF66] text-black">
              <CardTitle className="text-xl font-black flex items-center gap-2">
                <Activity className="w-6 h-6" />
                PERFORMANCE
              </CardTitle>
            </CardHeader>
            <CardContent className="p-4">
              <div className="space-y-2">
                <div className="flex justify-between font-bold">
                  <span>LATENCY:</span>
                  <span className="text-[#00FF66]">{Math.floor(Math.random() * 50 + 10)}ms</span>
                </div>
                <div className="flex justify-between font-bold">
                  <span>PACKET LOSS:</span>
                  <span className="text-[#00FF66]">0%</span>
                </div>
                <div className="flex justify-between font-bold">
                  <span>DATA:</span>
                  <span className="text-[#0066FF]">{(bytesTransferred / 1024).toFixed(1)}KB</span>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card className="border-4 border-black shadow-[6px_6px_0px_0px_#FFFF00] bg-white transform rotate-0.5">
            <CardHeader className="border-b-4 border-black bg-[#FFFF00] text-black">
              <CardTitle className="text-xl font-black flex items-center gap-2">
                <MapPin className="w-6 h-6" />
                LOCATION
              </CardTitle>
            </CardHeader>
            <CardContent className="p-4">
              <div className="space-y-2">
                <div className="flex justify-between font-bold">
                  <span>SERVERS:</span>
                  <span className="text-[#0066FF]">50+ COUNTRIES</span>
                </div>
                <div className="flex justify-between font-bold">
                  <span>NODES:</span>
                  <span className="text-[#00FF66]">1000+</span>
                </div>
                <div className="flex justify-between font-bold">
                  <span>LOAD:</span>
                  <span className="text-[#FF0066]">{Math.floor(Math.random() * 30 + 20)}%</span>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  );
}