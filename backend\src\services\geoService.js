const axios = require('axios');
const logger = require('../utils/logger');

class GeoService {
  constructor() {
    this.cache = new Map();
    this.cacheTimeout = 60 * 60 * 1000; // 1 hour
    this.apiEndpoints = [
      'http://ip-api.com/json/',
      'https://ipapi.co/json/',
      'https://api.ipify.org?format=json'
    ];
  }

  async getLocationInfo(ip = null) {
    try {
      const cacheKey = ip || 'current';
      const cached = this.cache.get(cacheKey);
      
      if (cached && Date.now() - cached.timestamp < this.cacheTimeout) {
        return cached.data;
      }

      let locationData = null;

      // Try ip-api.com first (free, no key required)
      try {
        const url = ip ? `http://ip-api.com/json/${ip}` : 'http://ip-api.com/json/';
        const response = await axios.get(url, {
          timeout: 5000,
          headers: {
            'User-Agent': 'ProxyForge-GeoService/1.0'
          }
        });

        if (response.data.status === 'success') {
          locationData = {
            ip: response.data.query,
            country: response.data.country,
            countryCode: response.data.countryCode,
            region: response.data.regionName,
            city: response.data.city,
            zip: response.data.zip,
            latitude: response.data.lat,
            longitude: response.data.lon,
            timezone: response.data.timezone,
            isp: response.data.isp,
            org: response.data.org,
            as: response.data.as,
            mobile: response.data.mobile || false,
            proxy: response.data.proxy || false,
            hosting: response.data.hosting || false,
            source: 'ip-api.com'
          };
        }
      } catch (error) {
        logger.warn('ip-api.com failed:', error.message);
      }

      // Fallback to ipapi.co
      if (!locationData) {
        try {
          const url = ip ? `https://ipapi.co/${ip}/json/` : 'https://ipapi.co/json/';
          const response = await axios.get(url, {
            timeout: 5000,
            headers: {
              'User-Agent': 'ProxyForge-GeoService/1.0'
            }
          });

          locationData = {
            ip: response.data.ip,
            country: response.data.country_name,
            countryCode: response.data.country_code,
            region: response.data.region,
            city: response.data.city,
            zip: response.data.postal,
            latitude: response.data.latitude,
            longitude: response.data.longitude,
            timezone: response.data.timezone,
            isp: response.data.org,
            org: response.data.org,
            as: response.data.asn,
            mobile: false,
            proxy: false,
            hosting: false,
            source: 'ipapi.co'
          };
        } catch (error) {
          logger.warn('ipapi.co failed:', error.message);
        }
      }

      // If all services fail, return default data
      if (!locationData) {
        locationData = {
          ip: ip || 'unknown',
          country: 'Unknown',
          countryCode: 'XX',
          region: 'Unknown',
          city: 'Unknown',
          zip: 'Unknown',
          latitude: 0,
          longitude: 0,
          timezone: 'UTC',
          isp: 'Unknown',
          org: 'Unknown',
          as: 'Unknown',
          mobile: false,
          proxy: false,
          hosting: false,
          source: 'fallback'
        };
      }

      // Add additional metadata
      locationData.flag = this.getCountryFlag(locationData.countryCode);
      locationData.continent = this.getContinent(locationData.countryCode);
      locationData.currency = this.getCurrency(locationData.countryCode);
      locationData.languages = this.getLanguages(locationData.countryCode);

      // Cache the result
      this.cache.set(cacheKey, {
        data: locationData,
        timestamp: Date.now()
      });

      return locationData;

    } catch (error) {
      logger.error('GeoService error:', error);
      throw new Error('Failed to get location information');
    }
  }

  async getCurrentLocation() {
    return this.getLocationInfo();
  }

  async getLocationByIP(ip) {
    if (!this.isValidIP(ip)) {
      throw new Error('Invalid IP address');
    }
    return this.getLocationInfo(ip);
  }

  getSupportedCountries() {
    return [
      { name: 'United States', code: 'US', flag: '🇺🇸' },
      { name: 'United Kingdom', code: 'GB', flag: '🇬🇧' },
      { name: 'Germany', code: 'DE', flag: '🇩🇪' },
      { name: 'France', code: 'FR', flag: '🇫🇷' },
      { name: 'Japan', code: 'JP', flag: '🇯🇵' },
      { name: 'Canada', code: 'CA', flag: '🇨🇦' },
      { name: 'Australia', code: 'AU', flag: '🇦🇺' },
      { name: 'Netherlands', code: 'NL', flag: '🇳🇱' },
      { name: 'Singapore', code: 'SG', flag: '🇸🇬' },
      { name: 'Brazil', code: 'BR', flag: '🇧🇷' },
      { name: 'India', code: 'IN', flag: '🇮🇳' },
      { name: 'South Korea', code: 'KR', flag: '🇰🇷' },
      { name: 'Italy', code: 'IT', flag: '🇮🇹' },
      { name: 'Spain', code: 'ES', flag: '🇪🇸' },
      { name: 'Sweden', code: 'SE', flag: '🇸🇪' },
      { name: 'Norway', code: 'NO', flag: '🇳🇴' },
      { name: 'Switzerland', code: 'CH', flag: '🇨🇭' },
      { name: 'Belgium', code: 'BE', flag: '🇧🇪' },
      { name: 'Austria', code: 'AT', flag: '🇦🇹' },
      { name: 'Denmark', code: 'DK', flag: '🇩🇰' }
    ];
  }

  getCountryFlag(countryCode) {
    if (!countryCode || countryCode === 'XX') return '🏳️';
    
    try {
      const codePoints = countryCode
        .toUpperCase()
        .split('')
        .map(char => 127397 + char.charCodeAt());
      
      return String.fromCodePoint(...codePoints);
    } catch (error) {
      return '🏳️';
    }
  }

  getContinent(countryCode) {
    const continents = {
      // North America
      'US': 'North America', 'CA': 'North America', 'MX': 'North America',
      
      // Europe
      'GB': 'Europe', 'DE': 'Europe', 'FR': 'Europe', 'IT': 'Europe', 
      'ES': 'Europe', 'NL': 'Europe', 'SE': 'Europe', 'NO': 'Europe',
      'CH': 'Europe', 'BE': 'Europe', 'AT': 'Europe', 'DK': 'Europe',
      
      // Asia
      'CN': 'Asia', 'JP': 'Asia', 'KR': 'Asia', 'IN': 'Asia', 'SG': 'Asia',
      
      // Oceania
      'AU': 'Oceania', 'NZ': 'Oceania',
      
      // South America
      'BR': 'South America', 'AR': 'South America', 'CL': 'South America',
      
      // Africa
      'ZA': 'Africa', 'NG': 'Africa', 'EG': 'Africa'
    };
    
    return continents[countryCode] || 'Unknown';
  }

  getCurrency(countryCode) {
    const currencies = {
      'US': 'USD', 'CA': 'CAD', 'GB': 'GBP', 'DE': 'EUR', 'FR': 'EUR',
      'IT': 'EUR', 'ES': 'EUR', 'NL': 'EUR', 'BE': 'EUR', 'AT': 'EUR',
      'JP': 'JPY', 'CN': 'CNY', 'AU': 'AUD', 'BR': 'BRL', 'IN': 'INR',
      'KR': 'KRW', 'SE': 'SEK', 'NO': 'NOK', 'CH': 'CHF', 'DK': 'DKK',
      'SG': 'SGD'
    };
    
    return currencies[countryCode] || 'Unknown';
  }

  getLanguages(countryCode) {
    const languages = {
      'US': ['English'], 'CA': ['English', 'French'], 'GB': ['English'],
      'DE': ['German'], 'FR': ['French'], 'JP': ['Japanese'],
      'CN': ['Chinese'], 'BR': ['Portuguese'], 'IN': ['Hindi', 'English'],
      'KR': ['Korean'], 'IT': ['Italian'], 'ES': ['Spanish'],
      'SE': ['Swedish'], 'NO': ['Norwegian'], 'CH': ['German', 'French', 'Italian'],
      'BE': ['Dutch', 'French'], 'AT': ['German'], 'DK': ['Danish'],
      'NL': ['Dutch'], 'AU': ['English'], 'SG': ['English', 'Chinese', 'Malay']
    };
    
    return languages[countryCode] || ['Unknown'];
  }

  isValidIP(ip) {
    const ipv4Regex = /^(?:(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\.){3}(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)$/;
    const ipv6Regex = /^(?:[0-9a-fA-F]{1,4}:){7}[0-9a-fA-F]{1,4}$/;
    
    return ipv4Regex.test(ip) || ipv6Regex.test(ip);
  }

  clearCache() {
    this.cache.clear();
    logger.info('GeoService cache cleared');
  }

  getCacheStats() {
    return {
      size: this.cache.size,
      timeout: this.cacheTimeout,
      entries: Array.from(this.cache.keys())
    };
  }
}

module.exports = GeoService;
