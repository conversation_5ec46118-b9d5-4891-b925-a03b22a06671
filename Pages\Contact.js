import React, { useState } from 'react';
import { <PERSON>, <PERSON><PERSON>ontent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Badge } from '@/components/ui/badge';
import { Mail, MessageSquare, Phone, MapPin, Clock, Send, AlertCircle } from 'lucide-react';
import { motion } from 'framer-motion';
import { SendEmail } from '@/integrations/Core';

export default function Contact() {
  const [formData, setFormData] = useState({
    name: '',
    email: '',
    subject: '',
    category: '',
    message: ''
  });
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [submitted, setSubmitted] = useState(false);

  const handleSubmit = async (e) => {
    e.preventDefault();
    setIsSubmitting(true);

    try {
      await SendEmail({
        to: '<EMAIL>',
        subject: `[${formData.category}] ${formData.subject}`,
        body: `
Name: ${formData.name}
Email: ${formData.email}
Category: ${formData.category}

Message:
${formData.message}
        `
      });
      
      setSubmitted(true);
      setFormData({ name: '', email: '', subject: '', category: '', message: '' });
    } catch (error) {
      console.error('Failed to send email:', error);
    }
    
    setIsSubmitting(false);
  };

  const contactMethods = [
    {
      icon: Mail,
      title: 'EMAIL SUPPORT',
      info: '<EMAIL>',
      description: 'Get help within 24 hours',
      color: '#FF0066'
    },
    {
      icon: MessageSquare,
      title: 'LIVE CHAT',
      info: 'Available 24/7',
      description: 'Instant brutal support',
      color: '#00FF66'
    },
    {
      icon: Phone,
      title: 'PHONE SUPPORT',
      info: '******-BRUTAL-1',
      description: 'Enterprise customers only',
      color: '#0066FF'
    },
    {
      icon: MapPin,
      title: 'HEADQUARTERS',
      info: 'San Francisco, CA',
      description: 'Silicon Valley HQ',
      color: '#FFFF00'
    }
  ];

  return (
    <div className="min-h-screen bg-white p-6 font-mono">
      <div className="max-w-6xl mx-auto">
        {/* Header */}
        <div className="mb-12 text-center">
          <h1 className="text-6xl font-black text-black mb-4 transform rotate-2">
            BRUTAL SUPPORT
          </h1>
          <p className="text-2xl text-black font-bold transform -rotate-1">
            GET HELP FROM THE EXPERTS
          </p>
        </div>

        {/* Contact Methods Grid */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-12">
          {contactMethods.map((method, index) => (
            <motion.div
              key={method.title}
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: index * 0.2 }}
            >
              <Card 
                className="border-4 border-black shadow-[6px_6px_0px_0px_#000000] bg-white text-center transform hover:rotate-2 transition-transform"
                style={{ borderColor: method.color }}
              >
                <CardContent className="p-6">
                  <div 
                    className="w-16 h-16 mx-auto mb-4 border-4 border-black flex items-center justify-center transform -rotate-12"
                    style={{ backgroundColor: method.color }}
                  >
                    <method.icon className="w-8 h-8 text-white" />
                  </div>
                  <h3 className="text-lg font-black mb-2">{method.title}</h3>
                  <p className="font-bold text-lg mb-2">{method.info}</p>
                  <p className="text-sm font-bold text-gray-600">{method.description}</p>
                </CardContent>
              </Card>
            </motion.div>
          ))}
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
          {/* Contact Form */}
          <Card className="border-4 border-black shadow-[12px_12px_0px_0px_#FF0066] bg-white transform -rotate-1">
            <CardHeader className="border-b-4 border-black bg-[#FF0066] text-white">
              <CardTitle className="text-3xl font-black">
                SEND BRUTAL MESSAGE
              </CardTitle>
            </CardHeader>
            <CardContent className="p-8">
              {submitted ? (
                <motion.div
                  initial={{ opacity: 0, scale: 0.8 }}
                  animate={{ opacity: 1, scale: 1 }}
                  className="text-center py-12"
                >
                  <div className="w-20 h-20 mx-auto mb-6 bg-[#00FF66] border-4 border-black rounded-full flex items-center justify-center">
                    <Send className="w-10 h-10 text-black" />
                  </div>
                  <h3 className="text-2xl font-black mb-4">MESSAGE SENT!</h3>
                  <p className="font-bold text-gray-600 mb-6">
                    We'll get back to you within 24 hours with a brutal response.
                  </p>
                  <Button
                    onClick={() => setSubmitted(false)}
                    className="font-black border-4 border-black shadow-[4px_4px_0px_0px_#000000] bg-[#0066FF] text-white hover:bg-[#0066FF]/90"
                  >
                    SEND ANOTHER MESSAGE
                  </Button>
                </motion.div>
              ) : (
                <form onSubmit={handleSubmit} className="space-y-6">
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div>
                      <Label className="text-lg font-black">YOUR NAME</Label>
                      <Input
                        required
                        value={formData.name}
                        onChange={(e) => setFormData({...formData, name: e.target.value})}
                        className="border-4 border-black text-lg font-bold h-12 mt-2"
                        placeholder="ENTER YOUR NAME"
                      />
                    </div>
                    <div>
                      <Label className="text-lg font-black">EMAIL ADDRESS</Label>
                      <Input
                        type="email"
                        required
                        value={formData.email}
                        onChange={(e) => setFormData({...formData, email: e.target.value})}
                        className="border-4 border-black text-lg font-bold h-12 mt-2"
                        placeholder="<EMAIL>"
                      />
                    </div>
                  </div>

                  <div>
                    <Label className="text-lg font-black">CATEGORY</Label>
                    <Select 
                      value={formData.category} 
                      onValueChange={(value) => setFormData({...formData, category: value})}
                    >
                      <SelectTrigger className="border-4 border-black text-lg font-bold h-12 mt-2">
                        <SelectValue placeholder="SELECT CATEGORY" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="technical">TECHNICAL SUPPORT</SelectItem>
                        <SelectItem value="billing">BILLING QUESTION</SelectItem>
                        <SelectItem value="feature">FEATURE REQUEST</SelectItem>
                        <SelectItem value="bug">BUG REPORT</SelectItem>
                        <SelectItem value="general">GENERAL INQUIRY</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>

                  <div>
                    <Label className="text-lg font-black">SUBJECT</Label>
                    <Input
                      required
                      value={formData.subject}
                      onChange={(e) => setFormData({...formData, subject: e.target.value})}
                      className="border-4 border-black text-lg font-bold h-12 mt-2"
                      placeholder="WHAT'S THIS ABOUT?"
                    />
                  </div>

                  <div>
                    <Label className="text-lg font-black">MESSAGE</Label>
                    <Textarea
                      required
                      value={formData.message}
                      onChange={(e) => setFormData({...formData, message: e.target.value})}
                      className="border-4 border-black text-lg font-bold mt-2 h-32"
                      placeholder="TELL US WHAT'S UP..."
                    />
                  </div>

                  <Button
                    type="submit"
                    disabled={isSubmitting}
                    className="w-full text-xl font-black py-6 border-4 border-black shadow-[6px_6px_0px_0px_#000000] bg-[#00FF66] text-black hover:bg-[#00FF66]/90 transform hover:rotate-1 transition-all"
                  >
                    {isSubmitting ? (
                      <>
                        <motion.div
                          animate={{ rotate: 360 }}
                          transition={{ duration: 1, repeat: Infinity, ease: "linear" }}
                          className="w-6 h-6 border-4 border-current border-t-transparent rounded-full mr-2"
                        />
                        SENDING...
                      </>
                    ) : (
                      <>
                        <Send className="w-6 h-6 mr-2" />
                        SEND BRUTAL MESSAGE
                      </>
                    )}
                  </Button>
                </form>
              )}
            </CardContent>
          </Card>

          {/* FAQ Section */}
          <Card className="border-4 border-black shadow-[12px_12px_0px_0px_#00FF66] bg-white transform rotate-1">
            <CardHeader className="border-b-4 border-black bg-[#00FF66] text-black">
              <CardTitle className="text-3xl font-black">
                BRUTAL FAQ
              </CardTitle>
            </CardHeader>
            <CardContent className="p-8">
              <div className="space-y-6">
                {[
                  {
                    question: 'HOW FAST IS GEOSHIFT PRO?',
                    answer: 'Our servers deliver 200+ Mbps speeds with military-grade encryption. No compromises.',
                    color: '#0066FF'
                  },
                  {
                    question: 'IS IT REALLY SECURE?',
                    answer: 'AES-256 encryption, kill switch, DNS leak protection, and zero-log policy. Brutally secure.',
                    color: '#FF0066'
                  },
                  {
                    question: 'HOW MANY DEVICES?',
                    answer: 'Connect up to 10 devices simultaneously on Pro plan. Unlimited on Enterprise.',
                    color: '#FFFF00'
                  },
                  {
                    question: 'REFUND POLICY?',
                    answer: '30-day money-back guarantee. No questions asked. Brutal honesty.',
                    color: '#00FF66'
                  }
                ].map((faq, index) => (
                  <motion.div
                    key={index}
                    initial={{ opacity: 0, x: 20 }}
                    animate={{ opacity: 1, x: 0 }}
                    transition={{ delay: index * 0.2 }}
                    className="p-4 border-4 border-black bg-black text-white transform hover:rotate-1 transition-transform"
                  >
                    <h4 
                      className="font-black text-lg mb-2"
                      style={{ color: faq.color }}
                    >
                      {faq.question}
                    </h4>
                    <p className="font-bold">{faq.answer}</p>
                  </motion.div>
                ))}
              </div>

              <div className="mt-8 p-6 border-4 border-black bg-[#FFFF00] text-black">
                <div className="flex items-center gap-3 mb-3">
                  <Clock className="w-6 h-6" />
                  <h4 className="text-xl font-black">RESPONSE TIMES</h4>
                </div>
                <div className="space-y-2 font-bold">
                  <div className="flex justify-between">
                    <span>FREE USERS:</span>
                    <Badge className="bg-black text-white border-2 border-black">48 HOURS</Badge>
                  </div>
                  <div className="flex justify-between">
                    <span>PRO USERS:</span>
                    <Badge className="bg-[#00FF66] text-black border-2 border-black">24 HOURS</Badge>
                  </div>
                  <div className="flex justify-between">
                    <span>ENTERPRISE:</span>
                    <Badge className="bg-[#FF0066] text-white border-2 border-black">2 HOURS</Badge>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  );
}