const axios = require('axios');

// Serverless function for geo-location lookup
exports.handler = async (event, context) => {
  // Set CORS headers
  const headers = {
    'Access-Control-Allow-Origin': '*',
    'Access-Control-Allow-Headers': 'Content-Type, Authorization',
    'Access-Control-Allow-Methods': 'GET, POST, OPTIONS',
    'Content-Type': 'application/json'
  };

  // Handle preflight requests
  if (event.httpMethod === 'OPTIONS') {
    return {
      statusCode: 200,
      headers,
      body: ''
    };
  }

  try {
    // Only allow GET requests
    if (event.httpMethod !== 'GET') {
      return {
        statusCode: 405,
        headers,
        body: JSON.stringify({ error: 'Method not allowed' })
      };
    }

    // Get IP address from query parameters or use client IP
    const queryParams = event.queryStringParameters || {};
    const targetIP = queryParams.ip || event.headers['x-forwarded-for'] || event.headers['x-real-ip'] || 'auto';

    // Rate limiting check (simple implementation)
    const clientIP = event.headers['x-forwarded-for'] || 'unknown';
    
    // In production, you would implement proper rate limiting with a database or cache
    // For now, we'll just log the request
    console.log(`Geo lookup request from ${clientIP} for IP: ${targetIP}`);

    let geoData = {};

    if (targetIP === 'auto' || !targetIP) {
      // Get client's geo information
      try {
        const response = await axios.get('http://ip-api.com/json/', {
          timeout: 5000,
          headers: {
            'User-Agent': 'ProxyForge-GeoLookup/1.0'
          }
        });

        if (response.data.status === 'success') {
          geoData = {
            ip: response.data.query,
            country: response.data.country,
            countryCode: response.data.countryCode,
            region: response.data.regionName,
            city: response.data.city,
            zip: response.data.zip,
            latitude: response.data.lat,
            longitude: response.data.lon,
            timezone: response.data.timezone,
            isp: response.data.isp,
            org: response.data.org,
            as: response.data.as,
            mobile: response.data.mobile || false,
            proxy: response.data.proxy || false,
            hosting: response.data.hosting || false
          };
        } else {
          throw new Error('Geo lookup failed');
        }
      } catch (error) {
        console.warn('ip-api.com failed, trying fallback');
        
        // Fallback to ipapi.co
        try {
          const fallbackResponse = await axios.get('https://ipapi.co/json/', {
            timeout: 5000,
            headers: {
              'User-Agent': 'ProxyForge-GeoLookup/1.0'
            }
          });

          geoData = {
            ip: fallbackResponse.data.ip,
            country: fallbackResponse.data.country_name,
            countryCode: fallbackResponse.data.country_code,
            region: fallbackResponse.data.region,
            city: fallbackResponse.data.city,
            zip: fallbackResponse.data.postal,
            latitude: fallbackResponse.data.latitude,
            longitude: fallbackResponse.data.longitude,
            timezone: fallbackResponse.data.timezone,
            isp: fallbackResponse.data.org,
            org: fallbackResponse.data.org,
            as: fallbackResponse.data.asn,
            mobile: false,
            proxy: false,
            hosting: false
          };
        } catch (fallbackError) {
          console.error('All geo services failed:', fallbackError.message);
          
          // Return default/unknown data
          geoData = {
            ip: 'unknown',
            country: 'Unknown',
            countryCode: 'XX',
            region: 'Unknown',
            city: 'Unknown',
            zip: 'Unknown',
            latitude: 0,
            longitude: 0,
            timezone: 'UTC',
            isp: 'Unknown',
            org: 'Unknown',
            as: 'Unknown',
            mobile: false,
            proxy: false,
            hosting: false
          };
        }
      }
    } else {
      // Look up specific IP address
      try {
        const response = await axios.get(`http://ip-api.com/json/${targetIP}`, {
          timeout: 5000,
          headers: {
            'User-Agent': 'ProxyForge-GeoLookup/1.0'
          }
        });

        if (response.data.status === 'success') {
          geoData = {
            ip: response.data.query,
            country: response.data.country,
            countryCode: response.data.countryCode,
            region: response.data.regionName,
            city: response.data.city,
            zip: response.data.zip,
            latitude: response.data.lat,
            longitude: response.data.lon,
            timezone: response.data.timezone,
            isp: response.data.isp,
            org: response.data.org,
            as: response.data.as,
            mobile: response.data.mobile || false,
            proxy: response.data.proxy || false,
            hosting: response.data.hosting || false
          };
        } else {
          return {
            statusCode: 404,
            headers,
            body: JSON.stringify({
              error: 'IP not found',
              message: 'The specified IP address could not be located'
            })
          };
        }
      } catch (error) {
        console.error('IP lookup failed:', error.message);
        
        return {
          statusCode: 500,
          headers,
          body: JSON.stringify({
            error: 'Lookup failed',
            message: 'Unable to lookup IP address information'
          })
        };
      }
    }

    // Add additional metadata
    const result = {
      ...geoData,
      flag: getCountryFlag(geoData.countryCode),
      continent: getContinent(geoData.countryCode),
      currency: getCurrency(geoData.countryCode),
      languages: getLanguages(geoData.countryCode),
      timestamp: new Date().toISOString(),
      source: 'ProxyForge GeoLookup API'
    };

    return {
      statusCode: 200,
      headers: {
        ...headers,
        'Cache-Control': 'public, max-age=3600' // Cache for 1 hour
      },
      body: JSON.stringify(result)
    };

  } catch (error) {
    console.error('Geo lookup error:', error);
    
    return {
      statusCode: 500,
      headers,
      body: JSON.stringify({
        error: 'Internal server error',
        message: 'Failed to perform geo lookup'
      })
    };
  }
};

// Helper functions
function getCountryFlag(countryCode) {
  if (!countryCode || countryCode === 'XX') return '🏳️';
  
  // Convert country code to flag emoji
  const codePoints = countryCode
    .toUpperCase()
    .split('')
    .map(char => 127397 + char.charCodeAt());
  
  return String.fromCodePoint(...codePoints);
}

function getContinent(countryCode) {
  const continents = {
    'US': 'North America', 'CA': 'North America', 'MX': 'North America',
    'GB': 'Europe', 'DE': 'Europe', 'FR': 'Europe', 'IT': 'Europe', 'ES': 'Europe', 'NL': 'Europe',
    'CN': 'Asia', 'JP': 'Asia', 'KR': 'Asia', 'IN': 'Asia', 'SG': 'Asia',
    'AU': 'Oceania', 'NZ': 'Oceania',
    'BR': 'South America', 'AR': 'South America', 'CL': 'South America',
    'ZA': 'Africa', 'NG': 'Africa', 'EG': 'Africa'
  };
  
  return continents[countryCode] || 'Unknown';
}

function getCurrency(countryCode) {
  const currencies = {
    'US': 'USD', 'CA': 'CAD', 'GB': 'GBP', 'DE': 'EUR', 'FR': 'EUR',
    'JP': 'JPY', 'CN': 'CNY', 'AU': 'AUD', 'BR': 'BRL', 'IN': 'INR'
  };
  
  return currencies[countryCode] || 'Unknown';
}

function getLanguages(countryCode) {
  const languages = {
    'US': ['English'], 'CA': ['English', 'French'], 'GB': ['English'],
    'DE': ['German'], 'FR': ['French'], 'JP': ['Japanese'],
    'CN': ['Chinese'], 'BR': ['Portuguese'], 'IN': ['Hindi', 'English']
  };
  
  return languages[countryCode] || ['Unknown'];
}
