import React, { useState, useEffect } from 'react';
import { User } from '@/entities/User';
import LoadingScreen from './LoadingScreen';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Shield, Lock, UserPlus } from 'lucide-react';
import { motion } from 'framer-motion';

export default function ProtectedRoute({ children, requireAuth = true }) {
  const [user, setUser] = useState(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);

  useEffect(() => {
    checkAuth();
  }, []);

  const checkAuth = async () => {
    try {
      const userData = await User.me();
      setUser(userData);
    } catch (err) {
      setError(err);
    }
    setLoading(false);
  };

  const handleLogin = async () => {
    try {
      await User.loginWithRedirect(window.location.href);
    } catch (err) {
      console.error('Lo<PERSON> failed:', err);
    }
  };

  if (loading) {
    return <LoadingScreen message="CHECKING BRUTAL CREDENTIALS..." />;
  }

  if (requireAuth && !user) {
    return (
      <div className="min-h-screen bg-black flex items-center justify-center p-6 font-mono">
        <motion.div
          initial={{ opacity: 0, scale: 0.8, rotate: -5 }}
          animate={{ opacity: 1, scale: 1, rotate: 0 }}
          transition={{ type: "spring", stiffness: 100, damping: 15 }}
        >
          <Card className="border-4 border-white shadow-[12px_12px_0px_0px_#FF0066] bg-white max-w-md w-full transform rotate-2">
            <CardHeader className="border-b-4 border-black bg-[#FF0066] text-white">
              <CardTitle className="text-3xl font-black flex items-center gap-4">
                <Shield className="w-8 h-8" />
                ACCESS DENIED
              </CardTitle>
            </CardHeader>
            <CardContent className="p-8 text-center">
              <div className="mb-8">
                <Lock className="w-24 h-24 mx-auto mb-6 text-[#FF0066]" />
                <h2 className="text-2xl font-black mb-4">BRUTAL LOGIN REQUIRED</h2>
                <p className="font-bold text-gray-600 mb-6">
                  You need to authenticate to access GeoShift Pro's brutal features.
                </p>
              </div>
              
              <div className="space-y-4">
                <Button
                  onClick={handleLogin}
                  className="w-full text-xl font-black py-6 border-4 border-black shadow-[6px_6px_0px_0px_#000000] bg-[#00FF66] text-black hover:bg-[#00FF66]/90 transform hover:rotate-1 transition-all"
                >
                  <UserPlus className="w-6 h-6 mr-2" />
                  LOGIN WITH GOOGLE
                </Button>
                
                <p className="text-sm font-bold text-gray-500">
                  Secure authentication powered by Google
                </p>
              </div>
            </CardContent>
          </Card>
        </motion.div>
      </div>
    );
  }

  return children;
}