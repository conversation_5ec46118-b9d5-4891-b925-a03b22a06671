import React, { createContext, useContext, useEffect, useState } from 'react';
import { io } from 'socket.io-client';
import { useAuth } from './useAuth';
import toast from 'react-hot-toast';

// Create Socket Context
const SocketContext = createContext();

// Socket Provider Component
export function SocketProvider({ children }) {
  const [socket, setSocket] = useState(null);
  const [connected, setConnected] = useState(false);
  const [proxyStats, setProxyStats] = useState(null);
  const [connectionStatus, setConnectionStatus] = useState('disconnected');
  const { user, isAuthenticated } = useAuth();

  // Initialize socket connection
  useEffect(() => {
    if (isAuthenticated && user) {
      const socketUrl = process.env.REACT_APP_SOCKET_URL || 'http://localhost:5000';
      
      const newSocket = io(socketUrl, {
        transports: ['websocket', 'polling'],
        timeout: 20000,
        forceNew: true,
        query: {
          userId: user.id
        }
      });

      // Connection event handlers
      newSocket.on('connect', () => {
        console.log('Socket connected:', newSocket.id);
        setConnected(true);
        setSocket(newSocket);
        
        // Join user-specific room
        newSocket.emit('join-room', user.id);
        
        toast.success('Real-time connection established', {
          duration: 2000,
          icon: '🔗'
        });
      });

      newSocket.on('disconnect', (reason) => {
        console.log('Socket disconnected:', reason);
        setConnected(false);
        
        if (reason === 'io server disconnect') {
          // Server disconnected, try to reconnect
          newSocket.connect();
        }
      });

      newSocket.on('connect_error', (error) => {
        console.error('Socket connection error:', error);
        setConnected(false);
        
        toast.error('Real-time connection failed', {
          duration: 3000,
          icon: '⚠️'
        });
      });

      // Proxy-specific event handlers
      newSocket.on('proxy-stats', (stats) => {
        setProxyStats(stats);
      });

      newSocket.on('proxy-status-change', (status) => {
        setConnectionStatus(status.status);
        
        if (status.status === 'connected') {
          toast.success(`Connected to ${status.location}`, {
            icon: '🌍'
          });
        } else if (status.status === 'disconnected') {
          toast.success('Disconnected from proxy', {
            icon: '🔌'
          });
        } else if (status.status === 'error') {
          toast.error(`Connection error: ${status.message}`, {
            icon: '❌'
          });
        }
      });

      newSocket.on('proxy-error', (error) => {
        console.error('Proxy error:', error);
        toast.error(error.message || 'Proxy operation failed', {
          icon: '⚠️'
        });
      });

      // Speed test results
      newSocket.on('speed-test-result', (result) => {
        toast.success(`Speed test: ${result.downloadSpeed} Mbps`, {
          icon: '⚡'
        });
      });

      // Subscription notifications
      newSocket.on('subscription-update', (update) => {
        if (update.type === 'upgraded') {
          toast.success(`Subscription upgraded to ${update.tier}!`, {
            icon: '🎉'
          });
        } else if (update.type === 'expired') {
          toast.error('Subscription expired', {
            icon: '⏰'
          });
        }
      });

      // Rate limit warnings
      newSocket.on('rate-limit-warning', (warning) => {
        toast.error(`Rate limit warning: ${warning.message}`, {
          icon: '⚠️'
        });
      });

      // System notifications
      newSocket.on('system-notification', (notification) => {
        const toastType = notification.type === 'error' ? 'error' : 
                         notification.type === 'warning' ? 'error' : 'success';
        
        toast[toastType](notification.message, {
          duration: notification.duration || 4000,
          icon: notification.icon || '📢'
        });
      });

      return () => {
        newSocket.close();
        setSocket(null);
        setConnected(false);
      };
    }
  }, [isAuthenticated, user]);

  // Request proxy stats
  const requestProxyStats = () => {
    if (socket && connected && user) {
      socket.emit('proxy-stats-request', { userId: user.id });
    }
  };

  // Start speed test
  const startSpeedTest = (proxyId) => {
    if (socket && connected) {
      socket.emit('speed-test-start', { proxyId });
      toast.loading('Running speed test...', {
        id: 'speed-test',
        duration: 10000
      });
    }
  };

  // Connect to proxy
  const connectToProxy = (proxyConfig) => {
    if (socket && connected) {
      socket.emit('proxy-connect', proxyConfig);
      setConnectionStatus('connecting');
      
      toast.loading('Connecting to proxy...', {
        id: 'proxy-connect',
        duration: 10000
      });
    }
  };

  // Disconnect from proxy
  const disconnectFromProxy = () => {
    if (socket && connected) {
      socket.emit('proxy-disconnect');
      setConnectionStatus('disconnecting');
      
      toast.loading('Disconnecting...', {
        id: 'proxy-disconnect',
        duration: 5000
      });
    }
  };

  // Send custom event
  const emit = (event, data) => {
    if (socket && connected) {
      socket.emit(event, data);
    }
  };

  // Subscribe to custom event
  const on = (event, callback) => {
    if (socket) {
      socket.on(event, callback);
      
      // Return cleanup function
      return () => socket.off(event, callback);
    }
  };

  // Unsubscribe from event
  const off = (event, callback) => {
    if (socket) {
      socket.off(event, callback);
    }
  };

  // Context value
  const value = {
    socket,
    connected,
    proxyStats,
    connectionStatus,
    requestProxyStats,
    startSpeedTest,
    connectToProxy,
    disconnectFromProxy,
    emit,
    on,
    off
  };

  return (
    <SocketContext.Provider value={value}>
      {children}
    </SocketContext.Provider>
  );
}

// Custom hook to use socket context
export function useSocket() {
  const context = useContext(SocketContext);
  
  if (context === undefined) {
    throw new Error('useSocket must be used within a SocketProvider');
  }
  
  return context;
}

export default SocketContext;
