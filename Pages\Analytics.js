import React, { useState, useEffect } from 'react';
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { Activity, Clock, Globe, Zap, TrendingUp, Download, Upload, MapPin } from 'lucide-react';
import { motion } from 'framer-motion';
import { BarChart, Bar, XAxis, YAxis, ResponsiveContainer, LineChart, Line, PieChart, Pie, Cell } from 'recharts';

export default function Analytics() {
  const [timeRange, setTimeRange] = useState('7d');
  const [analyticsData, setAnalyticsData] = useState({
    totalConnections: 0,
    totalUptime: 0,
    dataTransferred: 0,
    averageSpeed: 0,
    topCountries: [],
    speedHistory: [],
    connectionHistory: []
  });

  useEffect(() => {
    generateAnalyticsData();
  }, [timeRange]);

  const generateAnalyticsData = () => {
    // Generate mock analytics data
    const days = timeRange === '7d' ? 7 : timeRange === '30d' ? 30 : 90;
    
    const speedHistory = Array.from({ length: days }, (_, i) => ({
      day: `Day ${i + 1}`,
      speed: Math.floor(Math.random() * 100) + 50,
      uptime: Math.floor(Math.random() * 24) + 1
    }));

    const connectionHistory = Array.from({ length: days }, (_, i) => ({
      day: `Day ${i + 1}`,
      connections: Math.floor(Math.random() * 20) + 5,
      successful: Math.floor(Math.random() * 18) + 5
    }));

    const countries = [
      { name: 'United States', flag: '🇺🇸', connections: 45, data: 2.3 },
      { name: 'Germany', flag: '🇩🇪', connections: 32, data: 1.8 },
      { name: 'Japan', flag: '🇯🇵', connections: 28, data: 1.5 },
      { name: 'United Kingdom', flag: '🇬🇧', connections: 25, data: 1.2 },
      { name: 'Canada', flag: '🇨🇦', connections: 20, data: 1.1 }
    ];

    setAnalyticsData({
      totalConnections: Math.floor(Math.random() * 500) + 200,
      totalUptime: Math.floor(Math.random() * 168) + 50, // hours
      dataTransferred: Math.floor(Math.random() * 50) + 10, // GB
      averageSpeed: Math.floor(Math.random() * 50) + 120, // Mbps
      topCountries: countries,
      speedHistory,
      connectionHistory
    });
  };

  const pieColors = ['#FF0066', '#00FF66', '#0066FF', '#FFFF00', '#FF6600'];

  return (
    <div className="min-h-screen bg-white p-6 font-mono">
      <div className="max-w-7xl mx-auto">
        {/* Header */}
        <div className="mb-8">
          <h1 className="text-5xl font-black text-black mb-2 transform -rotate-1">
            BRUTAL ANALYTICS
          </h1>
          <p className="text-xl text-black font-bold">
            RAW DATA & PERFORMANCE METRICS
          </p>
        </div>

        {/* Time Range Selector */}
        <Card className="border-4 border-black shadow-[8px_8px_0px_0px_#000000] mb-8 bg-white transform rotate-0.5">
          <CardContent className="p-6">
            <div className="flex gap-4">
              {['7d', '30d', '90d'].map((range) => (
                <Button
                  key={range}
                  onClick={() => setTimeRange(range)}
                  className={`font-black border-4 border-black shadow-[4px_4px_0px_0px_#000000] transform hover:rotate-1 transition-transform ${
                    timeRange === range 
                      ? 'bg-[#FF0066] text-white' 
                      : 'bg-white text-black hover:bg-[#FFFF00]'
                  }`}
                >
                  LAST {range.toUpperCase()}
                </Button>
              ))}
            </div>
          </CardContent>
        </Card>

        {/* Stats Grid */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
          <motion.div
            initial={{ opacity: 0, x: -20 }}
            animate={{ opacity: 1, x: 0 }}
            transition={{ delay: 0.1 }}
          >
            <Card className="border-4 border-black shadow-[6px_6px_0px_0px_#FF0066] bg-white transform rotate-1">
              <CardHeader className="border-b-4 border-black bg-[#FF0066] text-white p-4">
                <CardTitle className="text-lg font-black flex items-center gap-2">
                  <Globe className="w-5 h-5" />
                  CONNECTIONS
                </CardTitle>
              </CardHeader>
              <CardContent className="p-4">
                <p className="text-3xl font-black text-black">{analyticsData.totalConnections}</p>
                <p className="text-sm font-bold text-[#666666]">TOTAL SESSIONS</p>
              </CardContent>
            </Card>
          </motion.div>

          <motion.div
            initial={{ opacity: 0, x: -20 }}
            animate={{ opacity: 1, x: 0 }}
            transition={{ delay: 0.2 }}
          >
            <Card className="border-4 border-black shadow-[6px_6px_0px_0px_#00FF66] bg-white transform -rotate-1">
              <CardHeader className="border-b-4 border-black bg-[#00FF66] text-black p-4">
                <CardTitle className="text-lg font-black flex items-center gap-2">
                  <Clock className="w-5 h-5" />
                  UPTIME
                </CardTitle>
              </CardHeader>
              <CardContent className="p-4">
                <p className="text-3xl font-black text-black">{analyticsData.totalUptime}H</p>
                <p className="text-sm font-bold text-[#666666]">TOTAL HOURS</p>
              </CardContent>
            </Card>
          </motion.div>

          <motion.div
            initial={{ opacity: 0, x: -20 }}
            animate={{ opacity: 1, x: 0 }}
            transition={{ delay: 0.3 }}
          >
            <Card className="border-4 border-black shadow-[6px_6px_0px_0px_#0066FF] bg-white transform rotate-0.5">
              <CardHeader className="border-b-4 border-black bg-[#0066FF] text-white p-4">
                <CardTitle className="text-lg font-black flex items-center gap-2">
                  <Download className="w-5 h-5" />
                  DATA
                </CardTitle>
              </CardHeader>
              <CardContent className="p-4">
                <p className="text-3xl font-black text-black">{analyticsData.dataTransferred}GB</p>
                <p className="text-sm font-bold text-[#666666]">TRANSFERRED</p>
              </CardContent>
            </Card>
          </motion.div>

          <motion.div
            initial={{ opacity: 0, x: -20 }}
            animate={{ opacity: 1, x: 0 }}
            transition={{ delay: 0.4 }}
          >
            <Card className="border-4 border-black shadow-[6px_6px_0px_0px_#FFFF00] bg-white transform -rotate-0.5">
              <CardHeader className="border-b-4 border-black bg-[#FFFF00] text-black p-4">
                <CardTitle className="text-lg font-black flex items-center gap-2">
                  <Zap className="w-5 h-5" />
                  SPEED
                </CardTitle>
              </CardHeader>
              <CardContent className="p-4">
                <p className="text-3xl font-black text-black">{analyticsData.averageSpeed}</p>
                <p className="text-sm font-bold text-[#666666]">AVG MBPS</p>
              </CardContent>
            </Card>
          </motion.div>
        </div>

        {/* Charts Grid */}
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-8 mb-8">
          {/* Speed History Chart */}
          <Card className="border-4 border-black shadow-[8px_8px_0px_0px_#FF0066] bg-white transform rotate-1">
            <CardHeader className="border-b-4 border-black bg-[#FF0066] text-white">
              <CardTitle className="text-xl font-black flex items-center gap-2">
                <TrendingUp className="w-6 h-6" />
                SPEED OVER TIME
              </CardTitle>
            </CardHeader>
            <CardContent className="p-6">
              <ResponsiveContainer width="100%" height={300}>
                <LineChart data={analyticsData.speedHistory}>
                  <XAxis dataKey="day" stroke="#000" strokeWidth={2} />
                  <YAxis stroke="#000" strokeWidth={2} />
                  <Line 
                    type="monotone" 
                    dataKey="speed" 
                    stroke="#00FF66" 
                    strokeWidth={4}
                    dot={{ fill: '#00FF66', strokeWidth: 3, r: 6 }}
                  />
                </LineChart>
              </ResponsiveContainer>
            </CardContent>
          </Card>

          {/* Connection History Chart */}
          <Card className="border-4 border-black shadow-[8px_8px_0px_0px_#00FF66] bg-white transform -rotate-1">
            <CardHeader className="border-b-4 border-black bg-[#00FF66] text-black">
              <CardTitle className="text-xl font-black flex items-center gap-2">
                <Activity className="w-6 h-6" />
                DAILY CONNECTIONS
              </CardTitle>
            </CardHeader>
            <CardContent className="p-6">
              <ResponsiveContainer width="100%" height={300}>
                <BarChart data={analyticsData.connectionHistory}>
                  <XAxis dataKey="day" stroke="#000" strokeWidth={2} />
                  <YAxis stroke="#000" strokeWidth={2} />
                  <Bar dataKey="connections" fill="#0066FF" stroke="#000" strokeWidth={2} />
                </BarChart>
              </ResponsiveContainer>
            </CardContent>
          </Card>
        </div>

        {/* Top Countries */}
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
          {/* Countries List */}
          <Card className="border-4 border-black shadow-[8px_8px_0px_0px_#0066FF] bg-white transform rotate-0.5">
            <CardHeader className="border-b-4 border-black bg-[#0066FF] text-white">
              <CardTitle className="text-xl font-black flex items-center gap-2">
                <MapPin className="w-6 h-6" />
                TOP COUNTRIES
              </CardTitle>
            </CardHeader>
            <CardContent className="p-6">
              <div className="space-y-4">
                {analyticsData.topCountries.map((country, index) => (
                  <motion.div
                    key={country.name}
                    initial={{ opacity: 0, x: -20 }}
                    animate={{ opacity: 1, x: 0 }}
                    transition={{ delay: index * 0.1 }}
                    className="flex items-center justify-between p-4 border-4 border-black bg-black text-white transform hover:rotate-1 transition-transform"
                  >
                    <div className="flex items-center gap-3">
                      <span className="text-2xl">{country.flag}</span>
                      <div>
                        <p className="font-black">{country.name}</p>
                        <p className="text-sm text-[#00FF66]">{country.connections} connections</p>
                      </div>
                    </div>
                    <div className="text-right">
                      <p className="font-black text-[#FFFF00]">{country.data}GB</p>
                      <p className="text-sm text-[#666666]">data used</p>
                    </div>
                  </motion.div>
                ))}
              </div>
            </CardContent>
          </Card>

          {/* Usage Pie Chart */}
          <Card className="border-4 border-black shadow-[8px_8px_0px_0px_#FFFF00] bg-white transform -rotate-0.5">
            <CardHeader className="border-b-4 border-black bg-[#FFFF00] text-black">
              <CardTitle className="text-xl font-black flex items-center gap-2">
                <Upload className="w-6 h-6" />
                USAGE BREAKDOWN
              </CardTitle>
            </CardHeader>
            <CardContent className="p-6">
              <ResponsiveContainer width="100%" height={300}>
                <PieChart>
                  <Pie
                    data={analyticsData.topCountries}
                    cx="50%"
                    cy="50%"
                    innerRadius={60}
                    outerRadius={100}
                    dataKey="connections"
                    stroke="#000"
                    strokeWidth={3}
                  >
                    {analyticsData.topCountries.map((entry, index) => (
                      <Cell key={`cell-${index}`} fill={pieColors[index % pieColors.length]} />
                    ))}
                  </Pie>
                </PieChart>
              </ResponsiveContainer>
              <div className="grid grid-cols-2 gap-2 mt-4">
                {analyticsData.topCountries.slice(0, 4).map((country, index) => (
                  <div key={country.name} className="flex items-center gap-2">
                    <div 
                      className="w-4 h-4 border-2 border-black"
                      style={{ backgroundColor: pieColors[index] }}
                    />
                    <span className="text-sm font-bold">{country.name}</span>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  );
}