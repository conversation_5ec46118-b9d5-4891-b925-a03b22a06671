{"name": "proxyforge-frontend", "version": "1.0.0", "description": "ProxyForge Frontend React Application", "private": true, "homepage": "https://proxyforge.netlify.app", "dependencies": {"@testing-library/jest-dom": "^5.17.0", "@testing-library/react": "^13.4.0", "@testing-library/user-event": "^14.5.1", "react": "^18.2.0", "react-dom": "^18.2.0", "react-router-dom": "^6.20.1", "react-scripts": "5.0.1", "web-vitals": "^2.1.4", "framer-motion": "^10.16.16", "lucide-react": "^0.294.0", "axios": "^1.6.2", "socket.io-client": "^4.7.4", "recharts": "^2.8.0", "react-hook-form": "^7.48.2", "react-hot-toast": "^2.4.1", "clsx": "^2.0.0", "tailwind-merge": "^2.1.0", "@headlessui/react": "^1.7.17", "@heroicons/react": "^2.0.18", "date-fns": "^2.30.0", "js-cookie": "^3.0.5", "react-query": "^3.39.3", "zustand": "^4.4.7"}, "devDependencies": {"@types/js-cookie": "^3.0.6", "autoprefixer": "^10.4.16", "postcss": "^8.4.32", "tailwindcss": "^3.3.6", "eslint": "^8.55.0", "eslint-config-react-app": "^7.0.1", "prettier": "^3.1.0", "@craco/craco": "^7.1.0"}, "scripts": {"start": "craco start", "build": "craco build", "test": "craco test", "eject": "react-scripts eject", "lint": "eslint src/", "lint:fix": "eslint src/ --fix", "format": "prettier --write src/", "analyze": "npm run build && npx bundle-analyzer build/static/js/*.js", "serve": "serve -s build -l 3000"}, "eslintConfig": {"extends": ["react-app", "react-app/jest"]}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}, "proxy": "http://localhost:5000"}