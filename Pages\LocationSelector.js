import React, { useState, useEffect } from 'react';
import { But<PERSON> } from "@/components/ui/button";
import { <PERSON>, CardContent, Card<PERSON>eader, CardTitle } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Badge } from "@/components/ui/badge";
import { Search, Globe, Zap, Shield, MapPin } from 'lucide-react';
import { motion, AnimatePresence } from 'framer-motion';
import { InvokeLLM } from '@/integrations/Core';

export default function LocationSelector() {
  const [locations, setLocations] = useState([]);
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedLocation, setSelectedLocation] = useState(null);
  const [isLoading, setIsLoading] = useState(true);
  const [filter, setFilter] = useState('all');

  useEffect(() => {
    loadLocations();
  }, []);

  const loadLocations = async () => {
    setIsLoading(true);
    try {
      const response = await InvokeLLM({
        prompt: "Generate a list of 20 countries with their flag emoji, capital city, approximate ping time (20-150ms), server load (20-80%), and connection speed (50-200 Mbps). Include a mix of popular and less common countries. Format as JSON array.",
        response_json_schema: {
          type: "object",
          properties: {
            locations: {
              type: "array",
              items: {
                type: "object",
                properties: {
                  country: { type: "string" },
                  flag: { type: "string" },
                  capital: { type: "string" },
                  ping: { type: "number" },
                  load: { type: "number" },
                  speed: { type: "number" },
                  tier: { type: "string", enum: ["premium", "standard", "basic"] }
                }
              }
            }
          }
        }
      });
      
      setLocations(response.locations || []);
    } catch (error) {
      console.error('Failed to load locations:', error);
      // Fallback data
      setLocations([
        { country: "United States", flag: "🇺🇸", capital: "Washington D.C.", ping: 25, load: 45, speed: 185, tier: "premium" },
        { country: "United Kingdom", flag: "🇬🇧", capital: "London", ping: 35, load: 55, speed: 170, tier: "premium" },
        { country: "Germany", flag: "🇩🇪", capital: "Berlin", ping: 40, load: 38, speed: 195, tier: "premium" },
        { country: "Japan", flag: "🇯🇵", capital: "Tokyo", ping: 120, load: 25, speed: 220, tier: "premium" },
        { country: "Australia", flag: "🇦🇺", capital: "Canberra", ping: 180, load: 60, speed: 145, tier: "standard" }
      ]);
    }
    setIsLoading(false);
  };

  const filteredLocations = locations.filter(location => {
    const matchesSearch = location.country.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         location.capital.toLowerCase().includes(searchTerm.toLowerCase());
    const matchesFilter = filter === 'all' || location.tier === filter;
    return matchesSearch && matchesFilter;
  });

  const getTierColor = (tier) => {
    switch (tier) {
      case 'premium': return 'bg-[#FFD700] text-black border-[#FFD700]';
      case 'standard': return 'bg-[#0066FF] text-white border-[#0066FF]';
      case 'basic': return 'bg-[#666666] text-white border-[#666666]';
      default: return 'bg-gray-500 text-white border-gray-500';
    }
  };

  const getSpeedColor = (speed) => {
    if (speed > 180) return 'text-[#00FF66]';
    if (speed > 120) return 'text-[#FFFF00]';
    return 'text-[#FF0066]';
  };

  const getPingColor = (ping) => {
    if (ping < 50) return 'text-[#00FF66]';
    if (ping < 100) return 'text-[#FFFF00]';
    return 'text-[#FF0066]';
  };

  return (
    <div className="min-h-screen bg-white p-6 font-mono">
      <div className="max-w-7xl mx-auto">
        {/* Header */}
        <div className="mb-8">
          <h1 className="text-5xl font-black text-black mb-2 transform rotate-1">
            CHOOSE YOUR LOCATION
          </h1>
          <p className="text-xl text-black font-bold">
            BRUTAL SERVER SELECTION
          </p>
        </div>

        {/* Controls */}
        <Card className="border-4 border-black shadow-[8px_8px_0px_0px_#000000] mb-8 bg-white transform -rotate-0.5">
          <CardHeader className="border-b-4 border-black bg-[#FF0066] text-white">
            <CardTitle className="text-2xl font-black flex items-center gap-4">
              <Search className="w-6 h-6" />
              FILTER & SEARCH
            </CardTitle>
          </CardHeader>
          <CardContent className="p-6">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div>
                <Input
                  placeholder="SEARCH COUNTRIES OR CAPITALS..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="border-4 border-black text-xl font-bold h-14 bg-white focus:bg-[#FFFF00] focus:text-black transition-colors"
                />
              </div>
              <div className="flex gap-4">
                {['all', 'premium', 'standard', 'basic'].map((tierFilter) => (
                  <Button
                    key={tierFilter}
                    onClick={() => setFilter(tierFilter)}
                    className={`font-black border-4 border-black shadow-[4px_4px_0px_0px_#000000] transform hover:rotate-1 transition-transform ${
                      filter === tierFilter 
                        ? 'bg-[#00FF66] text-black' 
                        : 'bg-white text-black hover:bg-[#FFFF00]'
                    }`}
                  >
                    {tierFilter.toUpperCase()}
                  </Button>
                ))}
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Location Grid */}
        {isLoading ? (
          <div className="text-center py-20">
            <motion.div
              animate={{ rotate: 360 }}
              transition={{ duration: 2, repeat: Infinity, ease: "linear" }}
              className="w-16 h-16 border-8 border-black border-t-[#FF0066] rounded-full mx-auto mb-4"
            />
            <p className="text-2xl font-black">LOADING BRUTAL SERVERS...</p>
          </div>
        ) : (
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            <AnimatePresence>
              {filteredLocations.map((location, index) => (
                <motion.div
                  key={location.country}
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  exit={{ opacity: 0, y: -20 }}
                  transition={{ delay: index * 0.1 }}
                >
                  <Card 
                    className={`border-4 border-black shadow-[6px_6px_0px_0px_#000000] bg-white cursor-pointer transform hover:rotate-1 hover:scale-105 transition-all ${
                      selectedLocation?.country === location.country ? 'bg-[#FFFF00] rotate-2 scale-105' : ''
                    }`}
                    onClick={() => setSelectedLocation(location)}
                  >
                    <CardHeader className="border-b-4 border-black bg-black text-white p-4">
                      <div className="flex items-center justify-between">
                        <div className="flex items-center gap-3">
                          <span className="text-3xl">{location.flag}</span>
                          <div>
                            <p className="font-black text-lg">{location.country}</p>
                            <p className="text-sm font-bold text-[#00FF66]">{location.capital}</p>
                          </div>
                        </div>
                        <Badge className={`border-4 font-black ${getTierColor(location.tier)}`}>
                          {location.tier.toUpperCase()}
                        </Badge>
                      </div>
                    </CardHeader>
                    <CardContent className="p-4">
                      <div className="grid grid-cols-2 gap-4 text-sm font-bold">
                        <div className="flex items-center gap-2">
                          <Zap className="w-4 h-4" />
                          <span className={getSpeedColor(location.speed)}>
                            {location.speed} MBPS
                          </span>
                        </div>
                        <div className="flex items-center gap-2">
                          <MapPin className="w-4 h-4" />
                          <span className={getPingColor(location.ping)}>
                            {location.ping}ms
                          </span>
                        </div>
                        <div className="flex items-center gap-2">
                          <Globe className="w-4 h-4" />
                          <span className="text-[#0066FF]">
                            {location.load}% LOAD
                          </span>
                        </div>
                        <div className="flex items-center gap-2">
                          <Shield className="w-4 h-4" />
                          <span className="text-[#00FF66]">
                            SECURE
                          </span>
                        </div>
                      </div>
                    </CardContent>
                  </Card>
                </motion.div>
              ))}
            </AnimatePresence>
          </div>
        )}

        {/* Selection Panel */}
        {selectedLocation && (
          <motion.div
            initial={{ opacity: 0, y: 50 }}
            animate={{ opacity: 1, y: 0 }}
            className="fixed bottom-6 left-6 right-6 z-50"
          >
            <Card className="border-4 border-black shadow-[8px_8px_0px_0px_#FF0066] bg-white transform rotate-1">
              <CardContent className="p-6">
                <div className="flex items-center justify-between">
                  <div className="flex items-center gap-4">
                    <span className="text-4xl">{selectedLocation.flag}</span>
                    <div>
                      <p className="text-2xl font-black">{selectedLocation.country}</p>
                      <p className="text-lg font-bold text-[#666666]">
                        {selectedLocation.capital} • {selectedLocation.speed} MBPS • {selectedLocation.ping}ms
                      </p>
                    </div>
                  </div>
                  <div className="flex gap-4">
                    <Button
                      onClick={() => setSelectedLocation(null)}
                      className="font-black border-4 border-black shadow-[4px_4px_0px_0px_#000000] bg-[#FF0066] text-white hover:bg-[#FF0066]/90"
                    >
                      CANCEL
                    </Button>
                    <Button
                      className="font-black border-4 border-black shadow-[4px_4px_0px_0px_#000000] bg-[#00FF66] text-black hover:bg-[#00FF66]/90 text-xl px-8"
                    >
                      CONNECT NOW
                    </Button>
                  </div>
                </div>
              </CardContent>
            </Card>
          </motion.div>
        )}
      </div>
    </div>
  );
}