{"name": "proxyforge", "version": "1.0.0", "description": "ProxyForge - Professional VPN/Proxy Management Platform", "private": true, "workspaces": ["frontend", "backend", "desktop"], "scripts": {"install:all": "npm install && npm run install:frontend && npm run install:backend && npm run install:desktop", "install:frontend": "cd frontend && npm install", "install:backend": "cd backend && npm install", "install:desktop": "cd desktop && npm install", "dev": "concurrently \"npm run dev:backend\" \"npm run dev:frontend\"", "dev:backend": "cd backend && npm run dev", "dev:frontend": "cd frontend && npm start", "dev:desktop": "cd desktop && npm run dev", "build": "npm run build:frontend && npm run build:backend", "build:frontend": "cd frontend && npm run build", "build:backend": "cd backend && npm run build", "build:desktop": "cd desktop && npm run build", "start": "npm run start:backend", "start:backend": "cd backend && npm start", "start:frontend": "cd frontend && npm start", "start:desktop": "cd desktop && npm start", "test": "npm run test:backend && npm run test:frontend", "test:backend": "cd backend && npm test", "test:frontend": "cd frontend && npm test", "lint": "npm run lint:backend && npm run lint:frontend", "lint:backend": "cd backend && npm run lint", "lint:frontend": "cd frontend && npm run lint", "lint:fix": "npm run lint:fix:backend && npm run lint:fix:frontend", "lint:fix:backend": "cd backend && npm run lint:fix", "lint:fix:frontend": "cd frontend && npm run lint:fix", "clean": "npm run clean:frontend && npm run clean:backend && npm run clean:desktop", "clean:frontend": "cd frontend && rm -rf build node_modules", "clean:backend": "cd backend && rm -rf dist node_modules logs", "clean:desktop": "cd desktop && rm -rf dist node_modules", "docker:dev": "docker-compose up -d postgres redis && npm run dev", "docker:prod": "docker-compose --profile production up -d", "docker:monitoring": "docker-compose --profile monitoring up -d", "docker:logging": "docker-compose --profile logging up -d", "docker:down": "docker-compose down", "docker:logs": "docker-compose logs -f", "deploy:netlify": "cd frontend && npm run build && netlify deploy --prod", "deploy:desktop": "cd desktop && npm run dist", "setup": "npm run install:all && npm run setup:env && npm run setup:db", "setup:env": "cp backend/.env.example backend/.env && echo 'Please update backend/.env with your configuration'", "setup:db": "cd backend && npm run db:migrate", "backup:db": "cd backend && npm run db:backup", "restore:db": "cd backend && npm run db:restore"}, "keywords": ["proxy", "vpn", "nodejs", "react", "electron", "privacy", "security", "networking"], "author": "HectorTa1989", "license": "MIT", "devDependencies": {"concurrently": "^8.2.2", "cross-env": "^7.0.3", "husky": "^8.0.3", "lint-staged": "^15.2.0", "prettier": "^3.1.0"}, "husky": {"hooks": {"pre-commit": "lint-staged"}}, "lint-staged": {"frontend/src/**/*.{js,jsx,ts,tsx}": ["cd frontend && npm run lint:fix", "cd frontend && npm run format"], "backend/src/**/*.{js,ts}": ["cd backend && npm run lint:fix"]}, "repository": {"type": "git", "url": "https://github.com/HectorTa1989/proxyforge.git"}, "bugs": {"url": "https://github.com/HectorTa1989/proxyforge/issues"}, "homepage": "https://github.com/HectorTa1989/proxyforge#readme", "engines": {"node": ">=18.0.0", "npm": ">=8.0.0"}}