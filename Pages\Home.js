import React, { useState, useEffect } from 'react';
import { <PERSON> } from 'react-router-dom';
import { createPageUrl } from '@/utils';
import { Button } from '@/components/ui/button';
import { Card, CardContent } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { 
  Globe, 
  Shield, 
  Zap, 
  MapPin, 
  ArrowRight, 
  Star,
  Users,
  Clock,
  CheckCircle
} from 'lucide-react';
import { motion } from 'framer-motion';

export default function Home() {
  const [stats, setStats] = useState({
    users: 0,
    countries: 0,
    connections: 0
  });

  useEffect(() => {
    // Animate stats on load
    const animateStats = () => {
      setTimeout(() => setStats({ users: 50000, countries: 85, connections: 2500000 }), 500);
    };
    animateStats();
  }, []);

  const features = [
    {
      icon: Globe,
      title: 'GLOBAL NETWORK',
      description: '85+ countries with high-speed servers',
      color: '#0066FF'
    },
    {
      icon: Shield,
      title: 'MILITARY ENCRYPTION', 
      description: 'AES-256 encryption with kill switch',
      color: '#FF0066'
    },
    {
      icon: Zap,
      title: 'LIGHTNING FAST',
      description: 'Optimized for maximum speed',
      color: '#00FF66'
    },
    {
      icon: Clock,
      title: '24/7 SUPPORT',
      description: 'Expert help when you need it',
      color: '#FFFF00'
    }
  ];

  return (
    <div className="min-h-screen bg-white font-mono">
      {/* Hero Section */}
      <section className="relative overflow-hidden bg-black text-white p-8 lg:p-16">
        <motion.div 
          initial={{ opacity: 0, y: 50 }}
          animate={{ opacity: 1, y: 0 }}
          className="max-w-6xl mx-auto text-center"
        >
          <h1 className="text-6xl lg:text-8xl font-black mb-6 transform -rotate-2">
            GEOSHIFT PRO
          </h1>
          <p className="text-2xl lg:text-3xl font-bold mb-8 text-[#00FF66] transform rotate-1">
            BRUTAL IP LOCATION SWITCHER
          </p>
          <p className="text-xl mb-12 max-w-3xl mx-auto font-bold">
            BYPASS RESTRICTIONS • PROTECT PRIVACY • CHANGE LOCATIONS INSTANTLY
          </p>
          
          <div className="flex flex-col sm:flex-row gap-6 justify-center items-center">
            <Link to={createPageUrl('Dashboard')}>
              <Button className="text-2xl font-black px-8 py-6 border-4 border-white shadow-[8px_8px_0px_0px_#FF0066] bg-[#FF0066] text-white hover:bg-[#FF0066]/90 transform hover:rotate-1 transition-all">
                START FREE TRIAL
                <ArrowRight className="w-6 h-6 ml-2" />
              </Button>
            </Link>
            <Badge className="text-lg px-6 py-3 bg-[#00FF66] text-black border-4 border-white font-black">
              7 DAYS FREE • NO CREDIT CARD
            </Badge>
          </div>
          
          {/* Stats */}
          <div className="grid grid-cols-1 md:grid-cols-3 gap-8 mt-16">
            <motion.div 
              initial={{ scale: 0 }}
              animate={{ scale: 1 }}
              transition={{ delay: 0.5 }}
              className="text-center"
            >
              <p className="text-4xl font-black text-[#00FF66]">{stats.users.toLocaleString()}+</p>
              <p className="text-lg font-bold">ACTIVE USERS</p>
            </motion.div>
            <motion.div 
              initial={{ scale: 0 }}
              animate={{ scale: 1 }}
              transition={{ delay: 0.7 }}
              className="text-center"
            >
              <p className="text-4xl font-black text-[#FFFF00]">{stats.countries}+</p>
              <p className="text-lg font-bold">COUNTRIES</p>
            </motion.div>
            <motion.div 
              initial={{ scale: 0 }}
              animate={{ scale: 1 }}
              transition={{ delay: 0.9 }}
              className="text-center"
            >
              <p className="text-4xl font-black text-[#0066FF]">{(stats.connections / 1000000).toFixed(1)}M+</p>
              <p className="text-lg font-bold">CONNECTIONS</p>
            </motion.div>
          </div>
        </motion.div>
      </section>

      {/* Features Section */}
      <section className="p-8 lg:p-16">
        <div className="max-w-6xl mx-auto">
          <h2 className="text-5xl font-black text-center mb-16 transform rotate-1">
            BRUTAL FEATURES
          </h2>
          
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
            {features.map((feature, index) => (
              <motion.div
                key={feature.title}
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ delay: index * 0.2 }}
              >
                <Card 
                  className="border-4 border-black shadow-[6px_6px_0px_0px_#000000] bg-white transform hover:rotate-2 hover:scale-105 transition-all"
                  style={{ borderColor: feature.color }}
                >
                  <CardContent className="p-6 text-center">
                    <div 
                      className="w-16 h-16 mx-auto mb-4 border-4 border-black flex items-center justify-center transform -rotate-12"
                      style={{ backgroundColor: feature.color }}
                    >
                      <feature.icon className="w-8 h-8 text-white" />
                    </div>
                    <h3 className="text-xl font-black mb-2">{feature.title}</h3>
                    <p className="font-bold text-gray-600">{feature.description}</p>
                  </CardContent>
                </Card>
              </motion.div>
            ))}
          </div>
        </div>
      </section>

      {/* Pricing Section */}
      <section className="p-8 lg:p-16 bg-[#FFFF00]">
        <div className="max-w-6xl mx-auto">
          <h2 className="text-5xl font-black text-center mb-16 transform -rotate-1">
            PRICING PLANS
          </h2>
          
          <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
            {/* Free Trial */}
            <Card className="border-4 border-black shadow-[8px_8px_0px_0px_#000000] bg-white transform rotate-2">
              <CardContent className="p-8 text-center">
                <h3 className="text-2xl font-black mb-4">FREE TRIAL</h3>
                <p className="text-4xl font-black mb-6">$0<span className="text-lg">/7 days</span></p>
                <ul className="space-y-2 mb-8 text-left font-bold">
                  <li className="flex items-center gap-2">
                    <CheckCircle className="w-5 h-5 text-[#00FF66]" />
                    5 Countries Available
                  </li>
                  <li className="flex items-center gap-2">
                    <CheckCircle className="w-5 h-5 text-[#00FF66]" />
                    Standard Speed
                  </li>
                  <li className="flex items-center gap-2">
                    <CheckCircle className="w-5 h-5 text-[#00FF66]" />
                    Basic Support
                  </li>
                </ul>
                <Button className="w-full font-black border-4 border-black shadow-[4px_4px_0px_0px_#000000] bg-[#00FF66] text-black hover:bg-[#00FF66]/90">
                  START FREE
                </Button>
              </CardContent>
            </Card>

            {/* Pro Plan - Most Popular */}
            <Card className="border-4 border-black shadow-[12px_12px_0px_0px_#FF0066] bg-white transform -rotate-1 scale-110 relative">
              <Badge className="absolute -top-4 left-1/2 transform -translate-x-1/2 bg-[#FF0066] text-white border-4 border-black font-black px-4 py-2">
                MOST POPULAR
              </Badge>
              <CardContent className="p-8 text-center">
                <h3 className="text-2xl font-black mb-4">PRO</h3>
                <p className="text-4xl font-black mb-6">$9<span className="text-lg">/month</span></p>
                <ul className="space-y-2 mb-8 text-left font-bold">
                  <li className="flex items-center gap-2">
                    <CheckCircle className="w-5 h-5 text-[#00FF66]" />
                    85+ Countries
                  </li>
                  <li className="flex items-center gap-2">
                    <CheckCircle className="w-5 h-5 text-[#00FF66]" />
                    Ultra-Fast Servers
                  </li>
                  <li className="flex items-center gap-2">
                    <CheckCircle className="w-5 h-5 text-[#00FF66]" />
                    Priority Support
                  </li>
                  <li className="flex items-center gap-2">
                    <CheckCircle className="w-5 h-5 text-[#00FF66]" />
                    Kill Switch
                  </li>
                </ul>
                <Button className="w-full font-black border-4 border-black shadow-[4px_4px_0px_0px_#000000] bg-[#FF0066] text-white hover:bg-[#FF0066]/90">
                  GET PRO
                </Button>
              </CardContent>
            </Card>

            {/* Enterprise */}
            <Card className="border-4 border-black shadow-[8px_8px_0px_0px_#000000] bg-white transform rotate-1">
              <CardContent className="p-8 text-center">
                <h3 className="text-2xl font-black mb-4">ENTERPRISE</h3>
                <p className="text-4xl font-black mb-6">$29<span className="text-lg">/month</span></p>
                <ul className="space-y-2 mb-8 text-left font-bold">
                  <li className="flex items-center gap-2">
                    <CheckCircle className="w-5 h-5 text-[#00FF66]" />
                    Everything in Pro
                  </li>
                  <li className="flex items-center gap-2">
                    <CheckCircle className="w-5 h-5 text-[#00FF66]" />
                    Dedicated Servers
                  </li>
                  <li className="flex items-center gap-2">
                    <CheckCircle className="w-5 h-5 text-[#00FF66]" />
                    24/7 Phone Support
                  </li>
                  <li className="flex items-center gap-2">
                    <CheckCircle className="w-5 h-5 text-[#00FF66]" />
                    Custom Solutions
                  </li>
                </ul>
                <Button className="w-full font-black border-4 border-black shadow-[4px_4px_0px_0px_#000000] bg-[#0066FF] text-white hover:bg-[#0066FF]/90">
                  CONTACT SALES
                </Button>
              </CardContent>
            </Card>
          </div>
        </div>
      </section>

      {/* CTA Section */}
      <section className="p-8 lg:p-16 bg-black text-white">
        <div className="max-w-4xl mx-auto text-center">
          <h2 className="text-5xl font-black mb-8 transform rotate-2">
            READY TO GO BRUTAL?
          </h2>
          <p className="text-xl font-bold mb-8">
            JOIN 50,000+ USERS WHO TRUST GEOSHIFT PRO
          </p>
          <Link to={createPageUrl('Dashboard')}>
            <Button className="text-2xl font-black px-12 py-6 border-4 border-white shadow-[8px_8px_0px_0px_#00FF66] bg-[#00FF66] text-black hover:bg-[#00FF66]/90 transform hover:rotate-1 transition-all">
              START YOUR FREE TRIAL
            </Button>
          </Link>
        </div>
      </section>
    </div>
  );
}